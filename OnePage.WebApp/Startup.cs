using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using OnePage.Common;
using OnePage.Models.Models.AdminModel;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.OrgModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Services.Interfaces;
using OnePage.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.Json.Serialization;
using Amazon.Extensions.NETCore.Setup;
using Amazon.SQS;
using OnePage.Models.Models;
using OnePage.Models.Models.ENTEventModel;
using ITelemetryTrack = OnePage.WebApp.TelemetryServices.ITelemetryTrack;
using TelemetryTrack = OnePage.WebApp.TelemetryServices.TelemetryTrack;
using TokenContext = OnePage.Models.Models.TokenModel.TokenContext;
using OnePage.Services.ENTServices;

namespace OnePage.WebApp
{
    public class Startup
    {

        // DataContext weddb;

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            // this.weddb = weddb;
            // this.wetc = new TokenContext(configuration);
            // weddb = new DataContext(configuration);
            // _adminContext = new AdminContext(configuration);
            // _orgContext = new OrgContext(configuration);
            // _entEventContext = new ENTEventContext(configuration);
            // _peopleContext = new PeopleContext(configuration);
            // _entEventContext = new ENTEventContext(configuration);
            // _eventRdsContext = new EventRDSContext(configuration);
            //weddb = new DataContext();
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // Load appropriate appsettings.json based on environment
            try
            {
                var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

                // Build configuration with fallback to base appsettings.json
                var configBuilder = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

                // Add environment-specific configuration if environment is specified
                if (!string.IsNullOrEmpty(environment))
                {
                    var appSettingsFile = $"appsettings.{environment}.json";
                    configBuilder.AddJsonFile(appSettingsFile, optional: true, reloadOnChange: true);
                }

                var configuration = configBuilder.Build();

                services.AddControllersWithViews();
                services.AddHttpClient("WebClient", client => client.Timeout = TimeSpan.FromSeconds(600));
                services.AddHttpContextAccessor();
                //services.AddSwaggerGen(c =>
                //{
                //    c.SwaggerDoc("v1", new OpenApiInfo { Title = "OnePage.WebApp", Version = "v1" });
                //});
                services.AddScoped<ITelemetryTrack, TelemetryTrack>();
                services.AddApplicationInsightsTelemetry();
                services.AddCors();
                services.AddMemoryCache();
                services.AddAutoMapper(typeof(Startup));
                // DB contexts 
                // services.AddTransient<OrgContext>();
                // services.AddTransient<DataContext>();
                // services.AddTransient<PeopleContext>();
                // services.AddTransient<AdminContext>();
                // services.AddTransient<TokenContext>();
                // services.AddTransient<ContactContext>();
                // services.AddTransient<EventRDSContext>();
                // services.AddDbContext<OrgContext>(options =>
                //     options.UseSqlServer(Configuration["DBConnections:WEOrgConnectionString"]),sqlServerOptionsAction: sqlOptions =>
                // {
                //     sqlOptions.EnableRetryOnFailure();
                // }),ServiceLifetime.Transient);
                services.AddDbContext<OrgContext>(options =>
                        options.UseSqlServer(
                            Configuration["DBConnections:WEOrgConnectionString"],
                            sqlServerOptionsAction: sqlOptions =>
                            {
                                sqlOptions.EnableRetryOnFailure();
                            }));

                services.AddDbContext<DataContext>(options =>
                    options.UseSqlServer(Configuration["DBConnections:WEDataConnectionString"], sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));

                services.AddDbContext<PeopleContext>(options =>
                    options.UseSqlServer(Configuration["DBConnections:WEPeopleConnectionString"], sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));

                services.AddDbContext<AdminContext>(options =>
                    options.UseSqlServer(Configuration["DBConnections:WEAdminConnectionString"], sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));

                services.AddDbContext<TokenContext>(options =>
                    options.UseSqlServer(Configuration["DBConnections:WETokenConnectionString"], sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));

                services.AddDbContext<ContactContext>(options =>
                    options.UseSqlServer(Configuration["DBConnections:WEContactConnectionString"], sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));

                services.AddDbContext<EventRDSContext>(options =>
                    options.UseSqlServer(Configuration["DBConnections:WEEventRDSConnectionString"], sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));
                services.AddDbContext<ENTEventContext>(option =>
                    option.UseSqlServer(Configuration["DBConnections:WEENTEventConnectionString"], sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));




                services.AddScoped<IUploadService, UploadFileService>();
                services.AddScoped<IRedisCaching, RedisCaching>();
                services.AddScoped<ICustomCacheService, CustomCacheService>();

                //Services
                services.AddScoped<RestClient>();
                services.AddScoped<ResearchService>();
                services.AddScoped<CalendarService>();
                services.AddScoped<EventTranscriptService>();
                services.AddScoped<ProviderService>();
                services.AddScoped<PeopleDataService>();
                services.AddScoped<AuthService>();
                services.AddScoped<MetricsService>();
                services.AddScoped<ContactService>();
                services.AddScoped<EntResearchService>();
                services.AddScoped<BotService>();
                services.AddScoped<ContactService>();
                services.AddScoped<IPeopleFileService, PeopleIconFileService>();
                var awsOptions = new AWSOptions
                {
                    Profile = "default",
                    Region = Amazon.RegionEndpoint.EUWest3
                };


                services.AddDefaultAWSOptions(awsOptions);

                services.AddSwaggerGen(c =>
                {
                    c.SwaggerDoc("v1", new OpenApiInfo { Title = "OnePage.WebApp", Version = "v1" });
                    c.AddSecurityDefinition("GuidAuth", new OpenApiSecurityScheme
                    {
                        Description = "Guid Auth Token",
                        Name = "authToken",
                        In = ParameterLocation.Header,
                        Type = SecuritySchemeType.ApiKey,
                        Scheme = "GuidAuth"
                    });
                    c.AddSecurityRequirement(new OpenApiSecurityRequirement
{
    {
        new OpenApiSecurityScheme
        {
            Reference = new OpenApiReference
            {
                Type = ReferenceType.SecurityScheme,
                Id = "GuidAuth"
            }
        },
        new string[] { }
    }
});
                    c.CustomSchemaIds(type => type.FullName);
                });


                services.AddAWSService<IAmazonSQS>();
                services.AddScoped<ISqsMessageSender, SqsMessageSender>();

               

                services.AddEndpointsApiExplorer();
                services.AddSwaggerGen();
                //services.AddScoped<ProviderService>();

                services.AddHttpContextAccessor();
                // services.AddDbContext<OrgContext>(options =>
                // {
                //     options.UseSqlServer(
                //         Configuration.GetConnectionString("DefaultConnection"));
                // }, ServiceLifetime.Transient);
                services.AddApplicationInsightsTelemetry();
                //services.AddCors(options =>
                //{
                //    options.AddPolicy(name: "MyAllowSpecificOrigins",
                //        builder =>
                //        {
                //            builder.WithOrigins("https://app.get1page.com").AllowAnyHeader().AllowAnyMethod();
                //        });
                //});
                //services.AddTransient<OrgContext>();
                //services.AddTransient<DataContext>();
                //services.AddTransient<PeopleContext>();
                services.AddScoped<UserProviderCaching>();
                services.AddScoped<RequestResearchService>();
                services.AddScoped<LanguageTranslate>();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://AKA.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseSwagger();

            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "OnePage.WebApp v1")
            );

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            app.UseRouting();
            app.UseCors();
            app.UseCors(builder =>
            {
                builder.WithOrigins(new string[]
                    {
                        "https://eu-fc-ap-wc.azurewebsites.net/", "http://localhost:4200/","https://localhost:4200/", "http://localhost:3000/", "https://localhost:3000/",
                        "https://funnel-2ae28.web.app", "https://get1page.com", "https://app.get1page.com",
                        "https://demo.get1page.com", "https://www.go1.page.net","https://web.get1page.com",
                        "https://\nthunderous-dango-055d1a\n.netlify.app","https://\nauthget1page\n.netlify.app",
                        "https://auth.get1page.com","https://web.get1page.com"
                    })
                    .AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader();
            });
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
            });
            LoadGenericValues();
        }

        public void LoadGenericValues()
        {
            // try
            // {
            //     CommonData.headerList = weddb.Headers.ToList();
            //     CommonData.urlList = weddb.Urls.ToList();
            //     CommonData.identifierList = weddb.Identifiers.ToList();
            //     CommonData.providerList = weddb.Providers.ToList();
            //     CommonData.providerUrlList = weddb.ProviderUrls.ToList();
            // }
            // catch (Exception ex)
            // {
            // }
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<DataContext>();
                optionsBuilder.UseSqlServer(Configuration["DBConnections:WEDataConnectionString"]);

                using (var dbContext = new DataContext(optionsBuilder.Options))
                {
                    CommonData.headerList = dbContext.Headers.ToList();
                    CommonData.urlList = dbContext.Urls.ToList();
                    CommonData.identifierList = dbContext.Identifiers.ToList();
                    CommonData.providerList = dbContext.Providers.ToList();
                    CommonData.providerUrlList = dbContext.ProviderUrls.ToList();
                }
            }
            catch (Exception ex)
            {
                // Handle exceptions
            }
        }
    }
}