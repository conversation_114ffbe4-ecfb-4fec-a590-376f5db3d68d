//using ChargeBee.Api;
//using ChargeBee.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

using Microsoft.IdentityModel.Tokens;
using MongoDB.Bson.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OnePage.Common;
using OnePage.Models.Models;
using OnePage.Models.Models.AdminModel;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.OrgModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Services;
using OnePage.Services.Interfaces;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using OnePage.WebApp.TelemetryServices;
using System.Net.Http;
using System.Security.Cryptography;
using System.Threading.Tasks;
using static OnePage.Common.CommonData;
using static OnePage.Models.Models.PropertyModels.AuthModels;
using static OnePage.Models.Models.PropertyModels.DataModels;
using JsonConvert = Newtonsoft.Json.JsonConvert;
using static OnePage.WebApp.Controllers.LoginController;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using TokenContext = OnePage.Models.Models.TokenModel.TokenContext;
using Microsoft.AspNetCore.Authorization;
using static System.Net.Mime.MediaTypeNames;
using MongoDB.Driver.Core.Configuration;
using System.Xml;
using System.ComponentModel;
using System.Reflection;


namespace OnePage.WebApp.Controllers
{
    public class AuthController : Controller
    {
        private readonly PeopleContext wepdb;
        private readonly OrgContext weodb;
        private readonly AdminContext weadb;
        private readonly DataContext weddb;
        private readonly ProviderService providerService;
        private readonly BotService botService;
        private readonly OnePage.Models.Models.TokenModel.TokenContext wetdb;
        private List<Identifier> identifierList = new List<Identifier>();
        private List<OrgIdentifier> orgProviderIdentifierList = new List<OrgIdentifier>();
        private List<OrgIdentifier> orgProviderIdentifierList2 = new List<OrgIdentifier>();
        private List<Url> providerUrlList = new List<Url>();
        private List<TempList> nameValueList = new List<TempList>();
        private List<TempList> headersList = new List<TempList>();
        private List<TempList> urlsCompleted = new List<TempList>();
        private readonly OnePage.WebApp.TelemetryServices.ITelemetryTrack telemetryTracker;
        private readonly IRedisCaching _redisCaching;
        private readonly UserProviderCaching _userProviderCaching;
        private readonly AuthService _authService;
        private readonly IConfiguration _configuration;
        private readonly LanguageTranslate translate;

        public class TempList
        {
            public string Name { get; set; }
            public string Value { get; set; }
        }
        public AuthController(OnePage.WebApp.TelemetryServices.ITelemetryTrack tracker, IRedisCaching redisCaching, PeopleContext wepdb, OrgContext weodb, AdminContext weadb, DataContext weddb, TokenContext wetdb, BotService botService,
        ProviderService providerService, UserProviderCaching userProviderCaching, AuthService authService, IConfiguration configuration, LanguageTranslate translate)
        {
            this.providerService = providerService;
            this.botService = botService;
            _userProviderCaching = userProviderCaching;
            _authService = authService;
            _configuration = configuration;
            _redisCaching = redisCaching;
            this.wepdb = wepdb;
            this.weodb = weodb;
            this.weadb = weadb;
            this.weddb = weddb;
            this.wetdb = wetdb;
            this.telemetryTracker = telemetryTracker;
            this.translate = translate;
        }
        [HttpGet] // dont delete
        [Route("statusCheck")]
        public async Task<IActionResult> AWSElasticBeanstalkStatusCheck()
        {
            return Ok();
        }
        [Route("DA8AD27B")]
        [HttpPost]
        public async Task<IActionResult> PostCoupon(string CouponCode)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                return Ok();
                //if (!string.IsNullOrEmpty(CouponCode))
                //{
                //    var user = wepdb.Users.First(w => w.Id == userId && w.IsActive == true);
                //    if (user != null)
                //    {
                //        Models.Models.PeopleModel.Coupon coupon = wepdb.Coupons.FirstOrDefault(w => (w.UserId.ToString().StartsWith(CouponCode) || w.Id.ToString().StartsWith(CouponCode)) && w.IsCancelled == false);
                //        if (coupon != null)
                //        {
                //            user.InvitedBy = coupon.UserId.ToString();
                //            wepdb.SaveChanges();
                //            var onboarding = new OnboardController();
                //            await onboarding.ApplyOffer(user.Id, coupon.OfferId);
                //            return Ok(user.InvitedBy);
                //        }
                //        else
                //        {
                //            return BadRequest("Wrong Referral Code");
                //        }
                //    }
                //    else
                //    {
                //        return BadRequest("User not existing");
                //    }
                //}
                //else
                //{
                //    return BadRequest("Wrong Referral Code");
                //}
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex.ToString());
            }
        }
        public class DynamicCrmData
        {
            public string clientId { get; set; }

            public string clientSecret { get; set; }

            public string tenantId { get; set; }

            public string resourceUrl { get; set; }

            public bool isRelogin { get; set; }
        }



        [Route("BFF32333")]
        [HttpPost]
        public async Task<IActionResult> HandleDynamicCRM([FromBody] DynamicCrmData data)
        {
            string url = "";
            Guid provisionId = Guid.Empty;
            List<OnePage.Models.Models.PropertyModels.DataModels.IdentifiersModel> customIdentifier = new List<IdentifiersModel>();
            var userId = _authService.GetUser(Request);
            var tokenId = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                var userDetails = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
                var domain = userDetails.Email.Split('@')[1];
                Guid orgId = await weodb.OrgUsers.AsNoTracking()
                    .Where(x => x.UserId == userId && x.OrgId != CommonData.WhatElseCustomerOrgId && x.IsActive == true && x.Org.Name.ToLower() == domain.ToLower() && x.Org.AppId.ToLower() == CommonData.GlobalAppId.ToString().ToLower()).Select(w => w.OrgId).FirstOrDefaultAsync();
                if (!data.isRelogin)
                {
                    var orgProviderExist = await weodb.OrgProviders.FirstOrDefaultAsync(w => w.OrgId == orgId && w.ProviderId == CommonData.DynamicsCRMProviderId && w.IsActive == true);
                    if (orgProviderExist == null)
                    {
                        var orgProvider = new OrgProvider();
                        orgProvider.Id = Guid.NewGuid();
                        orgProvider.CreatedDate = DateTime.UtcNow;
                        orgProvider.ModifiedDate = DateTime.UtcNow;
                        orgProvider.IsActive = true;
                        orgProvider.OrgId = orgId;
                        orgProvider.ProviderId = CommonData.DynamicsCRMProviderId;
                        orgProvider.CanSaveForOffline = true;
                        orgProvider.ForceCallLog = true;
                        weodb.OrgProviders.Add(orgProvider);

                        var weOrgProvider = await weodb.OrgProviders.FirstOrDefaultAsync(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.DynamicsCRMProviderId && w.IsActive == true);
                        if (weOrgProvider != null)
                        {
                            var weOrgProviderIdentifiers = await weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToListAsync();
                            Guid DyTenenatId = Guid.Parse("9248B6A6-CC1E-4FB4-91BE-2FF106D1B546");
                            Guid DyClientIId = Guid.Parse("DDA6FC69-A83E-43CD-B7E6-97620E8610BB");
                            Guid DyClientSecretId = Guid.Parse("C4C747ED-3A53-447F-BC72-BEA46175C2F0");
                            Guid DyResourceUrl = Guid.Parse("0A37DD21-9884-4560-94B6-CD6BBE7031F5");

                            var urlQuery = from urls in weddb.Urls
                                           where weddb.ProviderUrls
                                                 .Where(providerUrl => providerUrl.ProviderId == CommonData.DynamicsCRMProviderId)
                                                 .Select(providerUrl => providerUrl.UrlId)
                                                 .Contains(urls.Id)
                                                 && urls.ShowOrder == 1001
                                           select urls;

                            foreach (var urls in urlQuery)
                            {
                                url = urls.Name;
                            }
                            var checkForTenantIdExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == DyTenenatId);
                            if (!checkForTenantIdExists)
                            {
                                var orgIdentifier = new OrgIdentifier();
                                orgIdentifier.Id = Guid.NewGuid();
                                orgIdentifier.OrgProviderId = weOrgProvider.Id;
                                orgIdentifier.IdentifierId = DyTenenatId;
                                orgIdentifier.IsActive = true;
                                orgIdentifier.CreatedDate = DateTime.UtcNow;
                                orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                orgIdentifier.ShowSequence = 1;
                                orgIdentifier.Value = data.tenantId;
                                weodb.OrgIdentifiers.Add(orgIdentifier);
                                await weodb.SaveChangesAsync();
                                weOrgProviderIdentifiers.Add(orgIdentifier);
                            }
                            var checkForRedirectUrlExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == CommonData.DyRedirectUrl);
                            if (!checkForRedirectUrlExists)
                            {
                                var orgIdentifier = new OrgIdentifier();
                                orgIdentifier.Id = Guid.NewGuid();
                                orgIdentifier.OrgProviderId = weOrgProvider.Id;
                                orgIdentifier.IdentifierId = CommonData.DyRedirectUrl;
                                orgIdentifier.IsActive = true;
                                orgIdentifier.CreatedDate = DateTime.UtcNow;
                                orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                orgIdentifier.ShowSequence = 1;
                                orgIdentifier.Value = "https://connect.get1page.com/Authenticated/10A5E65C09674CF7B959CE4CB06C4DD7";
                                weodb.OrgIdentifiers.Add(orgIdentifier);
                                await weodb.SaveChangesAsync();
                                weOrgProviderIdentifiers.Add(orgIdentifier);
                            }
                            var checkForScopeExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == CommonData.DyScope);
                            if (!checkForRedirectUrlExists)
                            {
                                var orgIdentifier = new OrgIdentifier();
                                orgIdentifier.Id = Guid.NewGuid();
                                orgIdentifier.OrgProviderId = weOrgProvider.Id;
                                orgIdentifier.IdentifierId = CommonData.DyScope;
                                orgIdentifier.IsActive = true;
                                orgIdentifier.CreatedDate = DateTime.UtcNow;
                                orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                orgIdentifier.ShowSequence = 1;
                                orgIdentifier.Value = "openid offline_access profile Application.ReadWrite.All Application.ReadWrite.OwnedBy user_impersonation Device.ReadWrite.All Directory.Read.All User.ReadBasic.All User.Read.All";
                                weodb.OrgIdentifiers.Add(orgIdentifier);
                                await weodb.SaveChangesAsync();
                                weOrgProviderIdentifiers.Add(orgIdentifier);
                            }

                            var checkForResourceUrlExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == DyResourceUrl);
                            if (!checkForResourceUrlExists)
                            {
                                var orgIdentifier = new OrgIdentifier();
                                orgIdentifier.Id = Guid.NewGuid();
                                orgIdentifier.OrgProviderId = weOrgProvider.Id;
                                orgIdentifier.IdentifierId = DyResourceUrl;
                                orgIdentifier.IsActive = true;
                                orgIdentifier.CreatedDate = DateTime.UtcNow;
                                orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                orgIdentifier.ShowSequence = 1;
                                orgIdentifier.Value = data.resourceUrl;
                                weodb.OrgIdentifiers.Add(orgIdentifier);
                                await weodb.SaveChangesAsync();
                                weOrgProviderIdentifiers.Add(orgIdentifier);
                            }
                            foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
                            {
                                var orgIdentifier = new OrgIdentifier();
                                orgIdentifier.Id = Guid.NewGuid();
                                orgIdentifier.OrgProviderId = orgProvider.Id;
                                orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
                                orgIdentifier.IsActive = true;
                                orgIdentifier.CreatedDate = DateTime.UtcNow;
                                orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                orgIdentifier.ShowSequence = 1;
                                if (weOrgProviderIdentifier.IdentifierId == DyTenenatId)
                                {
                                    orgIdentifier.Value = data.tenantId;
                                }
                                else if (weOrgProviderIdentifier.IdentifierId == DyClientIId)
                                {
                                    orgIdentifier.Value = data.clientId;
                                }
                                else if (weOrgProviderIdentifier.IdentifierId == DyClientSecretId)
                                {
                                    orgIdentifier.Value = data.clientSecret;
                                }
                                else if (weOrgProviderIdentifier.IdentifierId == DyResourceUrl)
                                {
                                    orgIdentifier.Value = data.resourceUrl;
                                }
                                else
                                {
                                    orgIdentifier.Value = weOrgProviderIdentifier.Value;
                                }
                                weodb.OrgIdentifiers.Add(orgIdentifier);
                                weodb.SaveChanges();
                                string IdentifierName = await weddb.Identifiers.AsNoTracking().Where(x => x.Id == weOrgProviderIdentifier.IdentifierId).Select(x => x.Name).FirstOrDefaultAsync();
                                if (!customIdentifier.Any(x => x.Name == IdentifierName))
                                {
                                    customIdentifier.Add(new IdentifiersModel { IId = Guid.NewGuid(), Name = IdentifierName, Value = orgIdentifier.Value });
                                }
                                else
                                {
                                    customIdentifier.First(x => x.Name == IdentifierName).Value = orgIdentifier.Value;
                                }

                            }
                        }
                    }
                    else
                    {
                        var weOrgProviderIdentifiers = await weodb.OrgIdentifiers.Where(w => w.OrgProviderId == orgProviderExist.Id && w.IsActive == true).ToListAsync();
                        Guid DyTenenatId = Guid.Parse("9248B6A6-CC1E-4FB4-91BE-2FF106D1B546");
                        Guid DyClientIId = Guid.Parse("DDA6FC69-A83E-43CD-B7E6-97620E8610BB");
                        Guid DyClientSecretId = Guid.Parse("C4C747ED-3A53-447F-BC72-BEA46175C2F0");
                        Guid DyResourceUrl = Guid.Parse("0A37DD21-9884-4560-94B6-CD6BBE7031F5");
                        var urlQuery = from urls in weddb.Urls
                                       where weddb.ProviderUrls
                                             .Where(providerUrl => providerUrl.ProviderId == CommonData.DynamicsCRMProviderId)
                                             .Select(providerUrl => providerUrl.UrlId)
                                             .Contains(urls.Id)
                                             && urls.ShowOrder == 1001
                                       select urls;

                        foreach (var urls in urlQuery)
                        {
                            url = urls.Name;
                        }
                        var checkForTenantIdExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == DyTenenatId);
                        if (!checkForTenantIdExists)
                        {
                            var orgIdentifier = new OrgIdentifier();
                            orgIdentifier.Id = Guid.NewGuid();
                            orgIdentifier.OrgProviderId = orgProviderExist.Id;
                            orgIdentifier.IdentifierId = DyTenenatId;
                            orgIdentifier.IsActive = true;
                            orgIdentifier.CreatedDate = DateTime.UtcNow;
                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
                            orgIdentifier.ShowSequence = 1;
                            orgIdentifier.Value = data.tenantId;
                            weodb.OrgIdentifiers.Add(orgIdentifier);
                            await weodb.SaveChangesAsync();
                            weOrgProviderIdentifiers.Add(orgIdentifier);
                        }
                        var checkForRedirectUrlExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == CommonData.DyRedirectUrl);
                        if (!checkForRedirectUrlExists)
                        {
                            var orgIdentifier = new OrgIdentifier();
                            orgIdentifier.Id = Guid.NewGuid();
                            orgIdentifier.OrgProviderId = orgProviderExist.Id;
                            orgIdentifier.IdentifierId = CommonData.DyRedirectUrl;
                            orgIdentifier.IsActive = true;
                            orgIdentifier.CreatedDate = DateTime.UtcNow;
                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
                            orgIdentifier.ShowSequence = 1;
                            orgIdentifier.Value = "https://connect.get1page.com/Authenticated/10A5E65C09674CF7B959CE4CB06C4DD7";
                            weodb.OrgIdentifiers.Add(orgIdentifier);
                            await weodb.SaveChangesAsync();
                            weOrgProviderIdentifiers.Add(orgIdentifier);
                        }
                        var checkForScopeExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == CommonData.DyScope);
                        if (!checkForRedirectUrlExists)
                        {
                            var orgIdentifier = new OrgIdentifier();
                            orgIdentifier.Id = Guid.NewGuid();
                            orgIdentifier.OrgProviderId = orgProviderExist.Id;
                            orgIdentifier.IdentifierId = CommonData.DyScope;
                            orgIdentifier.IsActive = true;
                            orgIdentifier.CreatedDate = DateTime.UtcNow;
                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
                            orgIdentifier.ShowSequence = 1;
                            orgIdentifier.Value = "openid offline_access profile Application.ReadWrite.All Application.ReadWrite.OwnedBy user_impersonation Device.ReadWrite.All Directory.Read.All User.ReadBasic.All User.Read.All";
                            weodb.OrgIdentifiers.Add(orgIdentifier);
                            weodb.SaveChanges();
                            weOrgProviderIdentifiers.Add(orgIdentifier);
                        }
                        var checkForResourceUrlExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == DyResourceUrl);
                        if (!checkForResourceUrlExists)
                        {
                            var orgIdentifier = new OrgIdentifier();
                            orgIdentifier.Id = Guid.NewGuid();
                            orgIdentifier.OrgProviderId = orgProviderExist.Id;
                            orgIdentifier.IdentifierId = DyResourceUrl;
                            orgIdentifier.IsActive = true;
                            orgIdentifier.CreatedDate = DateTime.UtcNow;
                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
                            orgIdentifier.ShowSequence = 1;
                            orgIdentifier.Value = data.resourceUrl;
                            weodb.OrgIdentifiers.Add(orgIdentifier);
                            weodb.SaveChanges();
                            weOrgProviderIdentifiers.Add(orgIdentifier);
                        }
                        foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
                        {
                            if (weOrgProviderIdentifier.IdentifierId == DyTenenatId)
                            {
                                weOrgProviderIdentifier.Value = data.tenantId;
                            }
                            else if (weOrgProviderIdentifier.IdentifierId == DyClientIId)
                            {
                                weOrgProviderIdentifier.Value = data.clientId;
                            }
                            else if (weOrgProviderIdentifier.IdentifierId == DyClientSecretId)
                            {
                                weOrgProviderIdentifier.Value = data.clientSecret;
                            }
                            else if (weOrgProviderIdentifier.IdentifierId == DyResourceUrl)
                            {
                                weOrgProviderIdentifier.Value = data.resourceUrl;
                            }
                            await weodb.SaveChangesAsync();
                            string IdentifierName = await weddb.Identifiers.Where(x => x.Id == weOrgProviderIdentifier.IdentifierId).Select(x => x.Name).FirstOrDefaultAsync();
                            if (!customIdentifier.Any(x => x.Name == IdentifierName))
                            {
                                customIdentifier.Add(new IdentifiersModel { IId = Guid.NewGuid(), Name = IdentifierName, Value = weOrgProviderIdentifier.Value });
                            }
                            else
                            {
                                customIdentifier.First(x => x.Name == IdentifierName).Value = weOrgProviderIdentifier.Value;
                            }
                        }
                    }
                }
                else
                {
                    var weOrgProvider = await weodb.OrgProviders.FirstOrDefaultAsync(w => w.OrgId == orgId && w.ProviderId == CommonData.DynamicsCRMProviderId && w.IsActive == true);
                    string urlQuery = (from urls in weddb.Urls
                                       where weddb.ProviderUrls
                                             .Where(providerUrl => providerUrl.ProviderId == CommonData.DynamicsCRMProviderId)
                                             .Select(providerUrl => providerUrl.UrlId)
                                             .Contains(urls.Id)
                                             && urls.ShowOrder == 1001
                                       select urls.Name).First();


                    url = urlQuery;

                    if (weOrgProvider != null)
                    {
                        var weOrgProviderIdentifiers = await weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToListAsync();
                        Guid DyTenenatId = Guid.Parse("9248B6A6-CC1E-4FB4-91BE-2FF106D1B546");
                        Guid DyClientIId = Guid.Parse("DDA6FC69-A83E-43CD-B7E6-97620E8610BB");
                        Guid DyClientSecretId = Guid.Parse("C4C747ED-3A53-447F-BC72-BEA46175C2F0");
                        Guid DyResourceUrl = Guid.Parse("0A37DD21-9884-4560-94B6-CD6BBE7031F5");
                        var checkForTenantIdExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == DyTenenatId);
                        if (!checkForTenantIdExists)
                        {
                            var orgIdentifier = new OrgIdentifier();
                            orgIdentifier.Id = Guid.NewGuid();
                            orgIdentifier.OrgProviderId = weOrgProvider.Id;
                            orgIdentifier.IdentifierId = DyTenenatId;
                            orgIdentifier.IsActive = true;
                            orgIdentifier.CreatedDate = DateTime.UtcNow;
                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
                            orgIdentifier.ShowSequence = 1;
                            orgIdentifier.Value = data.tenantId;
                            weodb.OrgIdentifiers.Add(orgIdentifier);
                            weodb.SaveChanges();
                            weOrgProviderIdentifiers.Add(orgIdentifier);
                        }
                        var checkForResourceUrlExists = weOrgProviderIdentifiers.Any(w => w.IdentifierId == DyResourceUrl);
                        if (!checkForResourceUrlExists)
                        {
                            var orgIdentifier = new OrgIdentifier();
                            orgIdentifier.Id = Guid.NewGuid();
                            orgIdentifier.OrgProviderId = weOrgProvider.Id;
                            orgIdentifier.IdentifierId = DyResourceUrl;
                            orgIdentifier.IsActive = true;
                            orgIdentifier.CreatedDate = DateTime.UtcNow;
                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
                            orgIdentifier.ShowSequence = 1;
                            orgIdentifier.Value = data.resourceUrl;
                            weodb.OrgIdentifiers.Add(orgIdentifier);
                            await weodb.SaveChangesAsync();
                            weOrgProviderIdentifiers.Add(orgIdentifier);
                        }
                        foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
                        {
                            if (weOrgProviderIdentifier.IdentifierId == DyTenenatId)
                            {
                                weOrgProviderIdentifier.Value = data.tenantId;
                            }
                            else if (weOrgProviderIdentifier.IdentifierId == DyClientIId)
                            {
                                weOrgProviderIdentifier.Value = data.clientId;
                            }
                            else if (weOrgProviderIdentifier.IdentifierId == DyClientSecretId)
                            {
                                weOrgProviderIdentifier.Value = data.clientSecret;
                            }
                            else if (weOrgProviderIdentifier.IdentifierId == DyResourceUrl)
                            {
                                weOrgProviderIdentifier.Value = data.resourceUrl;
                            }
                            await weodb.SaveChangesAsync();
                            string IdentifierName = weddb.Identifiers.Where(x => x.Id == weOrgProviderIdentifier.IdentifierId).Select(x => x.Name).FirstOrDefault();
                            if (!customIdentifier.Any(x => x.Name == IdentifierName))
                            {
                                customIdentifier.Add(new IdentifiersModel { IId = Guid.NewGuid(), Name = IdentifierName, Value = weOrgProviderIdentifier.Value });
                            }
                            else
                            {
                                customIdentifier.First(x => x.Name == IdentifierName).Value = weOrgProviderIdentifier.Value;
                            }

                        }
                    }

                }
                User user = _redisCaching.CheckForItem("userDetail_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddDays(1));

                var existingProvision = weodb.Provisions.Any(w => w.AppId == user.AppId && w.EmailAddress == user.Email
                        && w.ProviderId == CommonData.DynamicsCRMProviderId && w.OrgId == orgId && w.UserId == userId);
                if (!existingProvision)
                {
                    var provision1 = new Provision();
                    provision1.Id = Guid.NewGuid();
                    provision1.OrgId = orgId;
                    provision1.AppId = user.AppId;
                    provision1.UserCustomerId = user.ChargebeeCustomerId;
                    provision1.ProviderTypeId = CommonData.CRMProviderTyepId;
                    provision1.ProviderId = CommonData.DynamicsCRMProviderId;
                    provision1.UserId = user.Id;
                    provision1.UserProviderId = Guid.Empty;
                    provision1.CreatedDate = DateTime.UtcNow;
                    provision1.ModifiedDate = DateTime.UtcNow;
                    provision1.IsActive = true;
                    provision1.IsConverted = false;
                    provision1.IsEnterpriseConverted = false;
                    //provision.SanitizedNumber = user.SanitizedNumber;
                    provision1.IsRedeemed = false;
                    //provision.PhoneNumber = user.ProvidedNumber ?? "";
                    //provision.Salutation = user.Salutation;
                    provision1.FirstName = user.FirstName;
                    provision1.LastName = user.LastName;
                    provision1.MiddleName = user.MiddleName;
                    provision1.CountryId = user.CountryId;
                    provision1.UserId = user.Id;
                    provision1.IsFree = false;
                    provision1.IsProvisioned = true;
                    provision1.IsPayed = false;
                    provision1.IsRequested = false;
                    provision1.IsAccepted = true;
                    provision1.IsPurchasedByUser = false;
                    provision1.IsPurchasedOnAndroid = false;
                    provision1.IsPurchasedOnIos = false;
                    provision1.EmailAddress = user.Email;
                    provision1.IsClaimed = true;
                    provision1.IsTrial = false;
                    weodb.Provisions.Add(provision1);
                    await weodb.SaveChangesAsync();
                    provisionId = provision1.Id;
                }
                else
                {
                    var provision = weodb.Provisions.FirstOrDefault(w => w.AppId == user.AppId && w.EmailAddress == user.Email
                        && w.ProviderId == CommonData.DynamicsCRMProviderId && w.OrgId == orgId && w.UserId == userId);
                    provisionId = provision.Id;
                }
                if (!customIdentifier.Any(x => x.Name == "state"))
                {
                    customIdentifier.Add(new IdentifiersModel { IId = Guid.NewGuid(), Name = "state", Value = tokenId.ToString() + "_" + provisionId.ToString() });
                }
                else
                {
                    customIdentifier.First(x => x.Name == "state").Value = tokenId.ToString() + "_" + provisionId.ToString();
                }
                url = providerService.ReplaceIdentifiersInString(customIdentifier, url);
                return Ok(url);
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot2(ex.ToString());
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex.ToString());
            }

        }

        public class UserPlanModel
        {
            public bool PlanExists { get; set; }
            public string PlanName { get; set; }

        }
        [Route("6E57D530")]
        [HttpPost]
        public async Task<IActionResult> CreateFreemiumPlan()
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                var user = await wepdb.Users.AsNoTracking().FirstOrDefaultAsync(w => w.Id == userId);
                var EmailSplit = user.Email.Split('@');
                var domain = EmailSplit[1].ToLower();
                var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(w => w.DomainName.ToLower() == domain.ToLower());
                if (isFreeDomain)
                {
                    var nonVanishingCredits = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == userId && w.SettingId == CommonData.NonVanishingCreditSettingId);
                    nonVanishingCredits.Value = "3";
                    await wepdb.SaveChangesAsync();

                    var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == userId && w.SettingId == CommonData.ShowAdsSettingId);
                    if (adsSettings != null)
                    {
                        adsSettings.Value = "1";
                        await wepdb.SaveChangesAsync();
                    }
                }
                else
                {
                    var org = await weodb.Orgs.Where(x => x.Name.ToLower() == domain.ToLower() && x.AppId.ToLower() == CommonData.GlobalAppId.ToString().ToLower() && x.IsActive == true).AsNoTracking().FirstOrDefaultAsync();
                    if (org != null)
                    {
                        var orgCreditUsage = await weodb.OrgCreditsNews.Where(x => x.OrgId == org.Id).FirstOrDefaultAsync();
                        if (orgCreditUsage != null)
                        {
                            int creditsToUpdate = int.Parse(orgCreditUsage.Credits) + 3;
                            orgCreditUsage.Credits = creditsToUpdate.ToString();
                            await weodb.SaveChangesAsync();
                        }
                    }

                    var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == userId && w.SettingId == CommonData.ShowAdsSettingId);
                    if (adsSettings != null)
                    {
                        adsSettings.Value = "1";
                        await wepdb.SaveChangesAsync();
                    }
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }
        [Route("ADBB0399")]
        [HttpPost]
        public async Task<IActionResult> SendUserPlanData()
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                UserPlanModel userPlanModel = new UserPlanModel();
                var checkForPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.UserId == userId && w.IsActive == true);
                if (checkForPlan)
                {
                    var userPlanData = await wepdb.UserPlans.AsNoTracking().FirstOrDefaultAsync(w => w.UserId == userId && w.IsActive == true);
                    userPlanModel.PlanExists = true;
                    var planData = await weadb.Plans.AsNoTracking().FirstOrDefaultAsync(w => w.Id == userPlanData.PlanId);
                    if (planData != null)
                    {
                        userPlanModel.PlanName = planData.Name;
                    }
                    else
                    {
                        // app sumo case
                        var offerData = await weadb.Offers.AsNoTracking().FirstOrDefaultAsync(w => w.Id == userPlanData.PlanId);
                        if (offerData != null)
                        {
                            userPlanModel.PlanName = offerData.Name;
                        }
                    }
                }
                else
                {
                    userPlanModel.PlanExists = false;
                    userPlanModel.PlanName = "";
                }
                return Ok(userPlanModel);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }
        [Route("8AC940ED")]
        [HttpPost]
        public async Task<IActionResult> CreateBot(string aadObjId)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                TelegramService.SendMessageToTestBot2("create bot api call for: " + userId);
                if (!string.IsNullOrEmpty(aadObjId))
                {
                    TelegramService.SendMessageToTestBot2("aadobjId: " + aadObjId);
                    User user = _redisCaching.CheckForItem("userDetail_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddDays(1));

                    if (user != null)
                    {
                        BotDatum botdata = _redisCaching.CheckForItem("botDataInfo_" + aadObjId, () => wepdb.BotData.First(w => w.Data.Contains(aadObjId)), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<BotDatum>, DateTimeOffset.Now.AddMinutes(10));

                        if (botdata != null && botdata.Data != null)
                        {
                            var jsonBotData = botdata.Data;
                            var botData = JsonConvert.DeserializeObject<BotDataModel>(jsonBotData);
                            var orgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
                            Guid BotProviderId = Guid.Parse("825E7926-04DB-409F-8BCB-F48ED904156C");
                            var identifierList = weddb.Identifiers.Where(w => w.ProviderId == BotProviderId && w.IsActive == true).ToList();
                            if (identifierList.Count > 0)
                            {
                                foreach (var identifier in identifierList)
                                {
                                    if (identifier.Name == "blconversationid")
                                    {
                                        identifier.Value = botData.ConvId;
                                    }
                                    else if (identifier.Name == "blconversationtenantid")
                                    {
                                        identifier.Value = botData.ConvTenantId;
                                    }
                                    else if (identifier.Name == "blchannelid")
                                    {
                                        identifier.Value = botData.ChannelId;
                                    }
                                    else if (identifier.Name == "blconversationaadobjectid")
                                    {
                                        identifier.Value = botData.ConvAadObjId;
                                    }
                                    else if (identifier.Name == "blrecipientid")
                                    {
                                        identifier.Value = botData.RecipientId;
                                    }
                                    else if (identifier.Name == "blrecipientaadobjectid")
                                    {
                                        identifier.Value = botData.RecipientAadObjectId;
                                    }
                                    else if (identifier.Name == "blfromaadobjid")
                                    {
                                        identifier.Value = botData.FromAadObjId;
                                    }
                                }
                                foreach (var identifier in identifierList)
                                {
                                    try
                                    {
                                        if (identifier.Value != null && identifier.Value != "")
                                        {
                                            var checkForExisting = weddb.CopilotAadModels.Any(w => w.UserId == user.Id && w.IdentifierId == identifier.Id && w.AadValue == identifier.Value && w.IsActive == true);
                                            if (!checkForExisting)
                                            {
                                                CopilotAadModel copilot = new CopilotAadModel();
                                                copilot.Id = Guid.NewGuid();
                                                copilot.AadValue = identifier.Value;
                                                copilot.IdentifierId = identifier.Id;
                                                copilot.IsActive = true;
                                                copilot.UserId = user.Id;
                                                copilot.CreatedDate = DateTime.UtcNow;
                                                copilot.ModifiedDate = DateTime.UtcNow;
                                                weddb.CopilotAadModels.Add(copilot);
                                                weddb.SaveChanges();
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        telemetryTracker.TrackException(ex, userId);
                                        continue;
                                        // throw;
                                    }
                                }
                                //var isExistingBotUP = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.ProviderId == BotProviderId && w.IsActive == true);
                                var x = await _userProviderCaching.GetUserProvidersFromRedisOrDbAsyncWithUserId(userId,
                                    BotProviderId);
                                var isExistingBotUP = x != null;
                                if (!isExistingBotUP)
                                {
                                    var newUserProvider = new Models.Models.PeopleModel.UserProvider();
                                    newUserProvider.Id = Guid.NewGuid();
                                    newUserProvider.UserId = user.Id;
                                    newUserProvider.AppId = user.AppId;
                                    newUserProvider.OrgId = orgId;
                                    newUserProvider.ProviderId = BotProviderId;
                                    newUserProvider.EmailAddress = user.Email.ToLower();
                                    newUserProvider.IsAuthenticated = true;
                                    newUserProvider.IsActive = true;
                                    newUserProvider.Code = "";
                                    newUserProvider.ActiveFrom = DateTime.UtcNow;
                                    newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
                                    newUserProvider.IsProvisioned = false;
                                    newUserProvider.IsFree = true;
                                    newUserProvider.IsPayed = false;
                                    newUserProvider.IsFullSyncDone = false;
                                    newUserProvider.Source = 0;
                                    newUserProvider.PurchaseId = "";
                                    newUserProvider.PurchaseState = 0;
                                    newUserProvider.ProductId = "";
                                    newUserProvider.CreatedDate = DateTime.UtcNow;
                                    newUserProvider.ModifiedDate = DateTime.UtcNow;
                                    newUserProvider.IsRelogginRequired = false;
                                    newUserProvider.Priority = 100;
                                    int trialDays = 30;
                                    newUserProvider.IsTrial = false;
                                    // wepdb.UserProviders.Add(newUserProvider);
                                    // wepdb.SaveChanges();
                                    await _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider);

                                    foreach (var identifier in identifierList)
                                    {
                                        try
                                        {
                                            if (identifier.Value != null && identifier.Value != "")
                                            {

                                                var upIdentifiers = new Models.Models.PeopleModel.UserProviderIdentifier();
                                                upIdentifiers.Id = Guid.NewGuid();
                                                upIdentifiers.IdentifierId = identifier.Id;
                                                upIdentifiers.UserProviderId = newUserProvider.Id;
                                                upIdentifiers.Value = identifier.Value;
                                                upIdentifiers.IsActive = true;
                                                upIdentifiers.CreatedDate = DateTime.UtcNow;
                                                upIdentifiers.ModifiedDate = DateTime.UtcNow;
                                                // wepdb.UserProviderIdentifiers.Add(upIdentifiers);
                                                // wepdb.SaveChanges();
                                                await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(
                                                    upIdentifiers);
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            telemetryTracker.TrackException(ex, userId);
                                            continue;
                                            // throw;
                                        }
                                    }
                                }
                                else
                                {
                                    var upi = wepdb.UserProviderIdentifiers.Where(w => w.UserProviderId == x.Id).ToList();
                                    if (upi.Count == 0)
                                    {
                                        foreach (var identifier in identifierList)
                                        {
                                            try
                                            {
                                                if (identifier.Value != null && identifier.Value != "")
                                                {

                                                    var upIdentifiers = new Models.Models.PeopleModel.UserProviderIdentifier();
                                                    upIdentifiers.Id = Guid.NewGuid();
                                                    upIdentifiers.IdentifierId = identifier.Id;
                                                    upIdentifiers.UserProviderId = x.Id;
                                                    upIdentifiers.Value = identifier.Value;
                                                    upIdentifiers.IsActive = true;
                                                    upIdentifiers.CreatedDate = DateTime.UtcNow;
                                                    upIdentifiers.ModifiedDate = DateTime.UtcNow;
                                                    // wepdb.UserProviderIdentifiers.Add(upIdentifiers);
                                                    // wepdb.SaveChanges();
                                                    await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(
                                                        upIdentifiers);
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                telemetryTracker.TrackException(ex, userId);
                                                continue;
                                                // throw;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        return Ok();
                    }
                    else
                    {
                        return BadRequest("User not existing");
                    }
                }
                else
                {
                    return BadRequest("Wrong Oject Id");
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex.ToString());
            }
        }
        public List<TempList> HeaderList(List<Identifier> identifierList, Header header)
        {

            var headervalue = header.Prefix;
            foreach (var item in identifierList)
            {
                if (headervalue.Contains("{" + item.Name + "}"))
                {
                    if (item.Value != null)
                    {
                        headervalue = headervalue.Replace("{" + item.Name + "}", item.Value);
                        headersList.Add(new TempList { Name = header.Name, Value = headervalue });
                    }
                }
            }


            return headersList;
        }

        public List<TempList> OrgProviderList(List<Identifier> identifierList, Header header)
        {
            var headervalue = header.Prefix;
            foreach (var item in orgProviderIdentifierList)
            {
                var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == item.IdentifierId).Name;
                if (headervalue.Contains("{" + identifierName + "}"))
                {
                    headervalue = headervalue.Replace("{" + identifierName + "}", item.Value);
                    headersList.Add(new TempList { Name = header.Name, Value = headervalue });
                }
            }
            return headersList;
        }
        public class CouponModel
        {
            public string EmailId { get; set; }
            public Guid CouponId { get; set; }
            public string Source { get; set; }
        }
        public class OfferModel
        {
            public List<Guid> coupons { get; set; }
        }
        [Route("C6D9A73C")]
        [HttpPost]
        public async Task<IActionResult> CreateOffers([FromBody] OfferModel couponList)
        {
            try
            {
                foreach (var coupon in couponList.coupons)
                {
                    CouponNew couponNew = new CouponNew()
                    {
                        EventName = "App Sumo",
                        Id = coupon,
                        IsActive = true,
                        IsClaimed = false,
                        OfferId = CommonData.SalespersonOfferId
                    };
                    weadb.CouponNews.Add(couponNew);
                    await weadb.SaveChangesAsync();
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
        public enum CouponErrorEnum : byte
        {
            CouponAlreadyUsed = 1,
            CouponIsNotValid = 2
        }
        [Route("5BFAFAFF")]
        [HttpPost]
        public async Task<IActionResult> PostCoupon([FromBody] CouponModel coupon)
        {
            try
            {
                if (coupon == null)
                    return BadRequest("Input model is not correct");

                var checkForCoupon = await weadb.CouponNews.AnyAsync(w => w.Id == coupon.CouponId);
                if (checkForCoupon)
                {
                    try
                    {
                        int strt = coupon.EmailId.IndexOf('@') + 1;
                        string emailDomains = coupon.EmailId.Substring(strt);
                        bool isfreedomainPresent = await weddb.FreeDomains.AnyAsync(x => x.DomainName == emailDomains);
                        if (isfreedomainPresent)
                        {
                            var checkForEmailWhitelist = await wepdb.EmailWhiteLists.AnyAsync(w => w.Email == coupon.EmailId);
                            if (!checkForEmailWhitelist)
                            {
                                EmailWhiteList emailWhiteList = new EmailWhiteList()
                                {
                                    CreatedDate = DateTime.UtcNow,
                                    Email = coupon.EmailId,
                                    Id = Guid.NewGuid(),
                                    IsActive = true,
                                    ModifiedDate = DateTime.UtcNow
                                };
                                wepdb.EmailWhiteLists.Add(emailWhiteList);
                                await wepdb.SaveChangesAsync();
                            }
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    var checkForCouponValidity = await wepdb.UserCoupons.AnyAsync(w => w.CouponId == coupon.CouponId && w.IsActive == true);
                    if (!checkForCouponValidity)
                    {
                        UserCoupon userCoupon = new UserCoupon()
                        {
                            CouponId = coupon.CouponId,
                            CreatedDate = DateTime.UtcNow,
                            EmailId = coupon.EmailId,
                            Id = Guid.NewGuid(),
                            IsActive = true,
                            IsApplied = false,
                            IsRedeemed = true,
                            IsRefunded = false,
                            ModifiedDate = DateTime.UtcNow,
                            Source = coupon.Source
                        };
                        wepdb.UserCoupons.Add(userCoupon);
                        await wepdb.SaveChangesAsync();
                    }
                    else
                    {
                        var existingCoupon = await wepdb.UserCoupons.FirstOrDefaultAsync(w => w.CouponId == coupon.CouponId && w.IsActive == true);
                        if (existingCoupon.IsRedeemed == false)
                        {
                            existingCoupon.EmailId = coupon.EmailId;
                            existingCoupon.IsRedeemed = true;
                            existingCoupon.ModifiedDate = DateTime.UtcNow;
                            await wepdb.SaveChangesAsync();
                        }
                        else
                        {
                            return BadRequest(CouponErrorEnum.CouponAlreadyUsed);
                        }
                    }
                }
                else
                    return BadRequest(CouponErrorEnum.CouponIsNotValid);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }
        [HttpPost]
        [Route("E0B2EC0E")]
        public async Task<IActionResult> RedeemCouponFromInvite(string couponId)
        {
            try
            {
                var userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                if (userId == Guid.Empty)
                {
                    return Unauthorized();
                }

                if (string.IsNullOrEmpty(couponId))
                    return BadRequest(CommonData.ErrorCodes.E0003);
                int credits = 0;
                var checkForCouponForUser = await weadb.CouponNews.AsNoTracking().AnyAsync(w => w.Id.ToString().ToLower() == couponId.ToLower());
                if (!checkForCouponForUser)
                {
                    // not a app sumo coupon
                    var checkForInvitedByUser = await wepdb.Users.AsNoTracking().AnyAsync(w => w.Id.ToString().Contains(couponId));
                    if (checkForInvitedByUser)
                    {
                        var userData = await wepdb.Users.FirstOrDefaultAsync(w => w.Id == userId);
                        if (string.IsNullOrEmpty(userData.InvitedBy))
                        {
                            var invitedByUser = await wepdb.Users.FirstOrDefaultAsync(w => w.Id.ToString().Contains(couponId));
                            if (invitedByUser.Id == userId)
                            {
                                return BadRequest(CommonData.ErrorCodes.E0001);
                            }
                            var slice = userData.Email.Split('@');
                            var slice1 = invitedByUser.Email.Split('@');
                            var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == slice[1].ToLower());
                            var isFreeDomain1 = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == slice1[1].ToLower());
                            var orgUser = weodb.OrgUsers.Where(w =>
                        w.UserId == invitedByUser.Id && w.Org.Name.ToLower() == slice1[1].ToLower() && w.IsActive == true)
                    .Include(x => x.Org).ToList();

                            var orgId = orgUser
                                .Where(x => x.Org.AppId.ToLower() == invitedByUser.AppId.ToString().ToLower() &&
                                            x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();

                            var userOrgUser = weodb.OrgUsers.Where(w =>
                            w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                        .Include(x => x.Org).ToList();

                            var orgUsers = weodb.OrgUsers.Where(w => w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true).ToList();

                            var userOrgId = userOrgUser
                                .Where(x => x.Org.AppId.ToLower() == userData.AppId.ToString().ToLower() &&
                                            x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();

                            if (orgId == userOrgId)
                            {
                                var offerCreditValue = await weadb.Offers.AsNoTracking().FirstOrDefaultAsync(w => w.Id == CommonData.InviteForSameDomainOfferId);
                                credits = int.Parse(offerCreditValue.Credits);
                                await UpdateForCredits(isFreeDomain, userId, userOrgId, credits);
                            }
                            else
                            {
                                if (orgUsers.Count > 1)
                                {
                                    var offerCreditValue = await weadb.Offers.AsNoTracking().FirstOrDefaultAsync(w => w.Id == CommonData.InviteForOtherDomainOfferId);
                                    credits = int.Parse(offerCreditValue.Credits);
                                }
                                else
                                {
                                    var offerCreditValue = await weadb.Offers.AsNoTracking().FirstOrDefaultAsync(w => w.Id == CommonData.InviteForOtherNewDomainOfferId);
                                    credits = int.Parse(offerCreditValue.Credits);
                                }
                                await UpdateForCredits(isFreeDomain, userId, userOrgId, credits);
                                var offerCreditForInvitedBy = await weadb.Offers.AsNoTracking().FirstOrDefaultAsync(w => w.Id == CommonData.InvitedByOfferId);
                                var creditsForInvitedBy = int.Parse(offerCreditForInvitedBy.Credits);

                                await UpdateForCredits(isFreeDomain1, invitedByUser.Id, orgId, creditsForInvitedBy);

                            }
                            // update userinvitedby id for user
                            userData.InvitedBy = invitedByUser.Id.ToString();
                            await wepdb.SaveChangesAsync();
                        }
                        else
                        {
                            return BadRequest(CommonData.ErrorCodes.E0002);
                        }
                    }
                    else
                    {
                        return BadRequest(CommonData.ErrorCodes.E0001);
                    }
                }
                else
                {
                    return BadRequest(CommonData.ErrorCodes.E0001);
                }
                return Ok(credits);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
        private async Task UpdateForCredits(bool isFreeDomain, Guid userId, Guid orgId, int credits)
        {
            try
            {
                if (isFreeDomain)
                {
                    var isCreditPresent = await wepdb.UserSettings.Where(x => x.UserId == userId && x.SettingId == CommonData.NonVanishingCreditSettingId).FirstOrDefaultAsync();
                    if (isCreditPresent != null)
                    {
                        isCreditPresent.Value = (int.Parse(isCreditPresent.Value) + credits).ToString();
                        await wepdb.SaveChangesAsync();
                    }
                    else
                    {
                        UserSetting creditSetting = new UserSetting();
                        creditSetting.Id = Guid.NewGuid();
                        creditSetting.UserId = userId;
                        creditSetting.SettingId = CommonData.VanishingCreditSettingId;
                        creditSetting.CreatedDate = DateTime.UtcNow;
                        creditSetting.Value = credits.ToString();
                        creditSetting.ModifiedDate = DateTime.UtcNow;
                        creditSetting.IsActive = true;
                        creditSetting.IsUserUpdated = true;
                        creditSetting.IsAdminUpdated = true;
                        wepdb.UserSettings.Add(creditSetting);
                        await wepdb.SaveChangesAsync();
                    }
                    CreditUsage creditUsage = new CreditUsage();
                    creditUsage.Id = Guid.NewGuid();
                    creditUsage.UserId = userId;
                    creditUsage.RedeemDateTime = DateTime.UtcNow;
                    creditUsage.Credit = credits.ToString();
                    creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                    creditUsage.IsCredited = true;
                    creditUsage.IsCreditedByInvite = true;
                    weodb.CreditUsages.Add(creditUsage);
                    await weodb.SaveChangesAsync();
                }
                else
                {
                    var orgCreditUsage = await weodb.OrgCreditsNews.Where(x => x.OrgId == orgId).FirstOrDefaultAsync();
                    if (orgCreditUsage != null)
                    {
                        int creditsToUpdate = int.Parse(orgCreditUsage.Credits) + credits;
                        orgCreditUsage.Credits = creditsToUpdate.ToString();
                        await weodb.SaveChangesAsync();

                        CreditUsage creditUsage = new CreditUsage();
                        creditUsage.Id = Guid.NewGuid();
                        creditUsage.UserId = userId;
                        creditUsage.RedeemDateTime = DateTime.UtcNow;
                        creditUsage.Credit = credits.ToString();
                        creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                        creditUsage.IsCredited = true;
                        creditUsage.IsCreditedByInvite = true;
                        weodb.CreditUsages.Add(creditUsage);
                        await weodb.SaveChangesAsync();
                    }
                    else
                    {
                        OrgCreditsNew orgCredits = new OrgCreditsNew()
                        {
                            Credits = credits.ToString(),
                            Id = Guid.NewGuid(),
                            LastPurchaseDate = DateTime.UtcNow,
                            OrgId = orgId
                        };
                        weodb.OrgCreditsNews.Add(orgCredits);
                        await weodb.SaveChangesAsync();

                        CreditUsage creditUsage = new CreditUsage();
                        creditUsage.Id = Guid.NewGuid();
                        creditUsage.UserId = userId;
                        creditUsage.RedeemDateTime = DateTime.UtcNow;
                        creditUsage.Credit = credits.ToString();
                        creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                        creditUsage.IsCredited = true;
                        creditUsage.IsCreditedByInvite = true;
                        weodb.CreditUsages.Add(creditUsage);
                        await weodb.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        public class SSOPayloadModel
        {
            public string ssotoken { get; set; }
            public string appId { get; set; } = "92C4D496-63E2-4660-B38A-B1EBF1101A65";
            public string pId { get; set; } = "327915F0-677A-444C-99A8-1B4998A4623C";
        }
        //public static class EnumHelper
        //{
        //    public static string GetDescription(Enum value)
        //    {
        //        FieldInfo field = value.GetType().GetField(value.ToString());
        //        DescriptionAttribute? attribute = (DescriptionAttribute?)field?.GetCustomAttribute(typeof(DescriptionAttribute));
        //        return attribute?.Description ?? value.ToString();
        //    }
        //}
        [Route("3FB2ED07")]
        [HttpPost]
        public async Task<IActionResult> HandleTeamsSSO([FromBody] SSOPayloadModel payload)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("ssotoken: " + payload.ssotoken);
                Guid AppId = Guid.Parse(payload.appId);
                Guid providerId = Guid.Parse(payload.pId);
                var orgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
                var providerUrls = weddb.ProviderUrls.FirstOrDefault(w => w.ProviderId == providerId);
                identifierList = weddb.Identifiers.Where(w => w.ProviderId == providerUrls.ProviderId && w.IsActive == true).ToList();
                var orgProvider = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == orgId && w.IsActive == true && w.ProviderId == providerId && w.IsActive == true);
                orgProviderIdentifierList = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == orgProvider.Id && w.IsActive == true).ToList();

                providerUrlList = weddb.Urls.Where(w => w.ProviderUrls.Where(c => c.ProviderId == providerId && c.IsActive == true && w.ShowOrder > 2000 && w.ShowOrder < 3000)
                .Count() > 0).OrderBy(w => w.ShowOrder).ToList();
                foreach (var providerUrl in providerUrlList)
                {
                    TelegramService.SendMessageToTestBot("providerurl name: " + providerUrl.Name);
                    if (providerUrl.HasHeaders == true)
                    {
                        var headerList = weddb.Headers.Where(w => w.Urlid == providerUrl.Id && w.IsActive == true);
                        foreach (var header in headerList)
                        {
                            //headersList.Clear();
                            if (header.Prefix.Contains("{") == true)
                            {
                                var headervalue = header.Prefix;
                                foreach (var item in identifierList)
                                {
                                    if (headervalue.Contains("{" + item.Name + "}"))
                                    {
                                        if (item.Value != null)
                                        {
                                            headervalue = headervalue.Replace("{" + item.Name + "}", item.Value);
                                            headersList.Add(new TempList { Name = header.Name, Value = headervalue });
                                        }
                                    }
                                }
                                foreach (var item in orgProviderIdentifierList)
                                {
                                    var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == item.IdentifierId).Name;
                                    if (headervalue.Contains("{" + identifierName + "}"))
                                    {
                                        headervalue = headervalue.Replace("{" + identifierName + "}", item.Value);
                                        headersList.Add(new TempList { Name = header.Name, Value = headervalue });
                                    }
                                }
                            }
                            else
                            {
                                headersList.Add(new TempList { Name = header.Name, Value = header.Prefix });
                            }
                        }
                    }
                    var dataToReplace = "";
                    if (providerUrl.NeedsData) //amper
                    {
                        dataToReplace = providerUrl.Posts.FirstOrDefault(w => w.IsActive == true).Name;
                        foreach (var item in identifierList)
                        {
                            dataToReplace = dataToReplace.Replace("{" + item.Name + "}", item.Value);
                        }
                        foreach (var item in orgProviderIdentifierList)
                        {
                            var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == item.IdentifierId).Name;
                            dataToReplace = dataToReplace.Replace("{" + identifierName + "}", item.Value);
                        }
                    }
                    var finalURL = providerUrl.Name;
                    finalURL = FindAndReplaceIndentifiers(finalURL);
                    TelegramService.SendMessageToTestBot("final: " + finalURL);

                    if (providerUrl.Httptype == 1)
                    {
                        bool postRetuenValue = false;
                        //  TelegramService.SendMessageToTestBot("Final Url: " + finalURL);
                        postRetuenValue = await PostToken(payload.ssotoken, providerUrl);
                        CommonData.sbnew.AppendLine("Linkedin Post token api response: " + postRetuenValue);
                        TelegramService.SendMessageToTestBot("POST: " + postRetuenValue);
                        if (postRetuenValue == false)
                        {
                            return BadRequest();
                        }
                        headersList.Clear();
                    }
                    else if (providerUrl.Httptype == 2)
                    {
                        bool getRetuenValue = false;
                        getRetuenValue = await GetClient(finalURL, providerUrl, dataToReplace);
                        CommonData.sbnew.AppendLine("Linkedin me api response: " + getRetuenValue);
                        TelegramService.SendMessageToTestBot("GET: " + getRetuenValue);
                        if (getRetuenValue == false)
                        {
                            return BadRequest();
                        }
                        headersList.Clear();
                    }
                    urlsCompleted.Add(new TempList { Name = providerUrl.Id.ToString(), Value = "1" });
                }
                var provider = weddb.Providers.FirstOrDefault(w => w.Id == providerId);
                var emailId = provider.Name ?? "";
                bool mailExist = false;

                mailExist = identifierList.Any(w => w.Name.Contains("userprincipalname") && w.Value != null && w.Value != "");

                if (mailExist == false)
                {
                    var nameExist = identifierList.Any(w => w.Name.Contains("name") && w.Value != null && w.Value != "");
                    emailId = (nameExist == true) ? identifierList.FirstOrDefault(w => w.Name.Contains("name") && w.Value != null && w.Value != "").Value.ToLower().ToString() : "";
                    if (emailId == "")
                    {
                        emailId = (mailExist == true) ? identifierList.FirstOrDefault(w => w.Name.Contains("mail") && w.Value != null && w.Value != "").Value.ToLower().ToString() : "";//;
                    }
                }
                else
                {
                    emailId = (mailExist == true) ? identifierList.FirstOrDefault(w => w.Name.Contains("userprincipalname") && w.Value != null && w.Value != "").Value.ToLower().ToString() : "";
                }
                Guid orgID = CommonData.WhatElseCustomerOrgId;

                bool isProvisioned = false;
                bool isAccepted = true;
                bool isTrail = false;
                bool isClaimed = true;

                var userDomain = emailId.Split('@');
                var domainName = userDomain[1].ToLower();
                //var domain = user.Email.Split('@')[1];

                //   var isfreeDomain = weddb.FreeDomains.FirstOrDefault(w => w.DomainName.ToLower() == domainName.ToLower());
                try
                {

                    var emailSplit = emailId.Split('@');
                    if (emailSplit.Count() > 1)
                    {
                        string domainName1 = "";
                        domainName1 = emailSplit[0];
                        bool isFreeDomain1 = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == domainName1.ToLower() || domainName1.ToLower().Contains(".onmicrosoft.com"));



                        if (isFreeDomain1)
                        {
                            var isWhiteListed = await wepdb.EmailWhiteLists.AsNoTracking().AnyAsync(x => x.Email.ToLower() == emailId.ToLower());
                            if (!isWhiteListed)
                            {
                                return BadRequest(CommonData.ErrorCodes.E0004);
                            }

                        }

                    }

                }
                catch (Exception ex)
                {

                }
                var orgExist = weodb.Orgs.Any(w => w.Domains == domainName);
                if (orgExist == true)
                {
                    var org = weodb.Orgs.FirstOrDefault(w => w.Domains.ToLower() == domainName.ToLower());
                    orgID = org.Id;

                }
                else
                {
                    var country1 = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
                    var org = new OnePage.Models.Models.OrgModel.Org();
                    org.Id = Guid.NewGuid();
                    org.IsBeta = false;
                    org.AppId = CommonData.GlobalAppId.ToString();
                    org.ModifiedDate = DateTime.UtcNow;
                    org.CreatedDate = DateTime.UtcNow;
                    org.AllowedAccounts = 1;
                    org.Name = domainName.ToLower();
                    org.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
                    org.Countries = country1.Name + "|" + country1.Prefix;
                    org.Domains = domainName;
                    org.IsActive = true;
                    org.IsRestricted = false;
                    org.IsProvider = false;
                    org.IsPurchaser = false;
                    org.IsAdminGivenFree = false;
                    org.IsInternal = false;
                    weodb.Orgs.Add(org);
                    weodb.SaveChanges();

                    orgID = org.Id;
                    isProvisioned = true;
                    isAccepted = true;
                    isTrail = false;
                    isClaimed = false;
                }
                var orgProviderExist = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == orgID && w.ProviderId == providerId && w.IsActive == true);
                if (orgProviderExist == null)
                {
                    var op = new OrgProvider();
                    op.Id = Guid.NewGuid();
                    op.CreatedDate = DateTime.UtcNow;
                    op.ModifiedDate = DateTime.UtcNow;
                    op.IsActive = true;
                    op.OrgId = orgID;
                    op.ProviderId = provider.Id;
                    op.CanSaveForOffline = true;
                    op.ForceCallLog = true;
                    weodb.OrgProviders.Add(op);

                    var weOrgProvider = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == provider.Id && w.IsActive == true);
                    if (weOrgProvider != null)
                    {
                        var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();

                        foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
                        {
                            var orgIdentifier = new OrgIdentifier();
                            orgIdentifier.Id = Guid.NewGuid();
                            orgIdentifier.OrgProviderId = op.Id;
                            orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
                            orgIdentifier.IsActive = true;
                            orgIdentifier.CreatedDate = DateTime.UtcNow;
                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
                            orgIdentifier.ShowSequence = 1;
                            orgIdentifier.Value = weOrgProviderIdentifier.Value;
                            weodb.OrgIdentifiers.Add(orgIdentifier);
                            weodb.SaveChanges();
                        }
                    }
                }

                var authUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 17 && w.AppId.ToString().ToLower() == AppId.ToString().ToLower() && w.IsActive).Url;
                var tempUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 17 && w.AppId.ToString().ToLower() == AppId.ToString().ToLower() && w.IsActive).Url;
                emailId = (emailId == "") ? provider.Name : emailId;
                //  eId = emailId;
                string firstName = "";
                string lasttName = "";
                string profileId = "";
                string displayImage = "";
                string country = "";
                string role = "";
                AuthUserModel authUserModel = new AuthUserModel();
                ValidationModel validationModel = new ValidationModel();

                var displayname = identifierList.Any(w => w.Name.Contains("displayname") && w.Value != null && w.Value != "");
                if (displayname)
                {
                    firstName = identifierList.First(w => w.Name.Contains("displayname") && w.Value != null && w.Value != "").Value.ToString();
                }
                var fnameExists = identifierList.Any(w => w.Name.Contains("first_name") && w.Value != null && w.Value != "");
                if (fnameExists)
                {
                    firstName = identifierList.First(w => w.Name.Contains("first_name") && w.Value != null && w.Value != "").Value.ToString();
                }
                var lnameExists = identifierList.Any(w => w.Name.Contains("last_name") && w.Value != null && w.Value != "");
                if (lnameExists)
                {
                    lasttName = identifierList.First(w => w.Name.Contains("last_name") && w.Value != null && w.Value != "").Value.ToString();
                }
                var isRole = identifierList.Any(w => w.Name.Contains("jobtitle") && w.Value != null && w.Value != "");
                if (isRole)
                {
                    role = identifierList.First(w => w.Name.Contains("jobtitle") && w.Value != null && w.Value != "").Value.ToString();
                }
                // call serverzone api

                try
                {

                    var emailSplit = emailId.Split('@');
                    if (emailSplit.Count() > 1)
                    {
                        string domainName1 = "";
                        domainName1 = emailSplit[0];
                        bool isFreeDomain1 = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == domainName1.ToLower() || domainName1.ToLower().Contains(".onmicrosoft.com"));

                        ErrorCodes errorCode = ErrorCodes.E0004;
                        //string description = EnumHelper.GetDescription(errorCode);

                        if (isFreeDomain1)
                        {
                            var isWhiteListed = await wepdb.EmailWhiteLists.AsNoTracking().AnyAsync(x => x.Email.ToLower() == emailId.ToLower());
                            if (!isWhiteListed)
                            {
                                return BadRequest("E0004");
                            }

                        }

                    }

                }
                catch (Exception ex)
                {

                }
                Guid dId1 = Guid.NewGuid();
                Guid dtId1 = Guid.Parse("467aae32-8361-43a3-a8c7-be21e32c4f3b");
                authUserModel.AppId = AppId;
                authUserModel.CountryId = country;
                authUserModel.EmailAddress = emailId;
                authUserModel.PhoneNumber = "";
                authUserModel.Password = dId1.ToString().ToLower();
                authUserModel.Coupon = "";
                authUserModel.IsPreAuthenticated = true;
                authUserModel.FirstName = firstName;
                authUserModel.LastName = lasttName;
                authUserModel.LoginFlowId = providerId;
                authUserModel.DeviceId = Guid.NewGuid();
                authUserModel.DeviceType = dtId1;

                var response1 = await ProcessServerZoneLogin(authUserModel);

                //  TelegramService.SendMessageToTestBot(response1.StatusCode.ToString());
                // call validate api
                validationModel.IsPreAuthenticated = true;
                validationModel.EmailAddress = emailId;
                validationModel.AppId = AppId;
                validationModel.DeviceId = dId1;
                validationModel.DeviceTypeId = dtId1;
                validationModel.OTP = dId1.ToString().ToLower();
                validationModel.LanguageId = "en-US";

                //var validateJson1 = JsonConvert.SerializeObject(validationModel);
                // authUrl = authUrl + "165C8F0E";
                //TelegramService.SendMessageToTestBot(authUrl);
                //TelegramService.SendMessageToTestBot(validateJson1);

                SSOReturnModel Djson4 = await ProcessValidateApi(validationModel);

                User userData = wepdb.Users.FirstOrDefault(w => w.Email == emailId && w.AppId == AppId);

                var uId = userData.Id;
                // try
                // {
                //     bool istestUser = (bool)userData.IsTestUser;
                //     if (!istestUser)
                //     {
                //         int strt = userData.Email.IndexOf('@') + 1;
                //         string emailDomains = userData.Email.Substring(strt);
                //         bool isfreedomainPresent = weddb.FreeDomains.Any(x => x.DomainName == emailDomains);
                //         bool emailWhiteListExist = wepdb.EmailWhiteLists.Any(w => w.Email.ToLower() == userData.Email.ToLower() && w.IsActive == true);
                //         if (isfreedomainPresent && !emailWhiteListExist)
                //         {
                //             TelegramService.SendMessage("free domain bad request for user trying with email: " + userData.Email);
                //             return BadRequest("Free Domain Access Denied");
                //         }
                //     }
                // }
                // catch (Exception ex)
                // {
                //     TelegramService.SendMessageToTestBot2(ex.ToString());
                // }

                if (Djson4.UserStatusModel.Registration == false)
                {
                    try
                    {
                        string registerUrl = "https://onepageregister-aafffehvdbctc9ba.northeurope-01.azurewebsites.net";
                        UserDetailModel regModel = new UserDetailModel();
                        regModel.Company = "";
                        regModel.Designation = "";
                        regModel.Email = emailId;
                        regModel.FirstName = firstName;
                        regModel.LastName = lasttName;
                        regModel.UserId = uId;
                        regModel.AppId = AppId;

                        var token = wetdb.Tokens.First(w => w.UserId == uId && w.IsActive == true).Id;
                        var json2 = JsonConvert.SerializeObject(regModel);
                        registerUrl = registerUrl + "59187A47";
                        var client4 = new RestSharp.RestClient(registerUrl);
                        var request4 = new RestRequest(Method.POST);
                        request4.AddHeader("content-type", "application/json;charset=UTF-8");
                        request4.AddHeader("authToken", token.ToString());
                        request4.AddParameter("application/json;charset=UTF-8", json2, ParameterType.RequestBody);
                        var response4 = await client4.ExecuteAsync(request4);
                        TelegramService.SendMessageToTestBot(response4.StatusCode.ToString());
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex, Guid.Empty);
                        TelegramService.SendMessageToTestBot(ex.ToString());
                    }
                }

                User user = wepdb.Users.FirstOrDefault(w => w.Email == emailId && w.AppId == AppId);
                if (user != null)
                {
                    TelegramService.SendMessageToTestBot(user.Email);
                    TelegramService.SendMessageToTestBot(user.Id.ToString());

                    // Guid teamsProviderId = Guid.Parse(urlproviderId);
                    var isExistingUP = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.ProviderId == providerId && w.IsActive == true && w.EmailAddress.ToLower() == emailId.ToLower() && w.AppId == user.AppId);
                    if (isExistingUP)
                    {
                        TelegramService.SendMessageToTestBot("IsExisting");
                        var existingUP = wepdb.UserProviders.First(w => w.UserId == user.Id && w.ProviderId == providerId && w.IsActive == true && w.EmailAddress.ToLower() == emailId.ToLower() && w.AppId == user.AppId);
                        TelegramService.SendMessageToTestBot("up id: " + existingUP.Id.ToString());
                        foreach (var identifier in identifierList)
                        {
                            try
                            {
                                if (identifier.Value != null && identifier.Value != "")
                                {
                                    // var isExistingUI = wepdb.UserProviderIdentifiers.Any(w => w.UserProviderId == existingUP.Id && w.IdentifierId == identifier.Id);
                                    // var x = await _userProviderCaching.GetUserProviderIdentifiersFromRedisOrDb(
                                    //     existingUP.ProviderId, identifier.Id);
                                    var x = existingUP.UserProviderIdentifiers.Where(x => x.IsActive == true).ToList();
                                    var isExistingUI = x.Count > 0 ? true : false;
                                    if (isExistingUI)
                                    {
                                        //var existingUI = wepdb.UserProviderIdentifiers.First(w => w.UserProviderId == existingUP.Id && w.IdentifierId == identifier.Id);

                                        var existingUI = x.FirstOrDefault();
                                        existingUI.Value = identifier.Value;
                                        existingUI.ModifiedDate = DateTime.UtcNow;
                                        await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(existingUI);
                                        // wepdb.SaveChanges();
                                    }
                                    else
                                    {
                                        var upIdentifiers = new Models.Models.PeopleModel.UserProviderIdentifier();
                                        upIdentifiers.Id = Guid.NewGuid();
                                        upIdentifiers.IdentifierId = identifier.Id;
                                        upIdentifiers.UserProviderId = existingUP.Id;
                                        upIdentifiers.Value = identifier.Value;
                                        upIdentifiers.IsActive = true;
                                        upIdentifiers.CreatedDate = DateTime.UtcNow;
                                        upIdentifiers.ModifiedDate = DateTime.UtcNow;
                                        // wepdb.UserProviderIdentifiers.Add(upIdentifiers);
                                        // wepdb.SaveChanges();

                                        await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(upIdentifiers);
                                    }

                                }
                            }
                            catch (Exception ex)
                            {
                                telemetryTracker.TrackException(ex, Guid.Empty);
                                continue;
                                // throw;
                            }
                        }
                    }
                    else
                    {
                        TelegramService.SendMessageToTestBot("Not Existing");
                        var newUserProvider = new Models.Models.PeopleModel.UserProvider();
                        newUserProvider.Id = Guid.NewGuid();
                        newUserProvider.UserId = user.Id;
                        newUserProvider.AppId = user.AppId;
                        newUserProvider.OrgId = orgId;
                        newUserProvider.ProviderId = providerId;
                        newUserProvider.EmailAddress = emailId.ToLower();
                        newUserProvider.IsAuthenticated = true;
                        newUserProvider.IsActive = true;
                        newUserProvider.Code = "";
                        newUserProvider.ActiveFrom = DateTime.UtcNow;
                        newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
                        newUserProvider.IsProvisioned = false;
                        newUserProvider.IsFree = true;
                        newUserProvider.IsPayed = false;
                        newUserProvider.IsFullSyncDone = false;
                        newUserProvider.Source = 0;
                        newUserProvider.PurchaseId = "";
                        newUserProvider.PurchaseState = 0;
                        newUserProvider.ProductId = "";
                        newUserProvider.CreatedDate = DateTime.UtcNow;
                        newUserProvider.ModifiedDate = DateTime.UtcNow;
                        newUserProvider.IsRelogginRequired = false;
                        newUserProvider.Priority = 100;
                        int trialDays = 30;
                        newUserProvider.IsTrial = false;
                        // wepdb.UserProviders.Add(newUserProvider);
                        // wepdb.SaveChanges();
                        await _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider);

                        foreach (var identifier in identifierList)
                        {
                            try
                            {
                                if (identifier.Value != null && identifier.Value != "")
                                {

                                    var upIdentifiers = new Models.Models.PeopleModel.UserProviderIdentifier();
                                    upIdentifiers.Id = Guid.NewGuid();
                                    upIdentifiers.IdentifierId = identifier.Id;
                                    upIdentifiers.UserProviderId = newUserProvider.Id;
                                    upIdentifiers.Value = identifier.Value;
                                    upIdentifiers.IsActive = true;
                                    upIdentifiers.CreatedDate = DateTime.UtcNow;
                                    upIdentifiers.ModifiedDate = DateTime.UtcNow;
                                    // wepdb.UserProviderIdentifiers.Add(upIdentifiers);
                                    // wepdb.SaveChanges();
                                    await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(upIdentifiers);
                                }
                            }
                            catch (Exception ex)
                            {
                                telemetryTracker.TrackException(ex, Guid.Empty);
                                continue;
                                // throw;
                            }
                        }
                        wepdb.SaveChanges();
                    }

                }

                var appInfo2 = weadb.
                Apps.FirstOrDefault(w => w.Id == AppId && w.IsActive);
                var androidOneSignalId2 = appInfo2.AndroidOneSignalId;
                var androidOneSignalKey2 = appInfo2.AndroidOneSignalApiKey;
                var IosOneSignalId2 = appInfo2.IosoneSignalId;
                var IosOneSignalKey2 = appInfo2.IosoneSignalApiKey;
                var WebOneSignalId2 = appInfo2.WebOneSignalId;
                var WebOneSignalKey2 = appInfo2.WebOneSignalApiKey;

                Guid provisionId = Guid.Empty;
                Guid userId = user.Id;
                Provision provision = new Provision();
                if (provisionId == Guid.Empty)
                {
                    bool shouldCreateUserEmail = !wepdb.UserEmails.Any(w => w.Email.ToLower() == emailId.ToLower() && w.UserId == user.Id);

                    var userEmailExists = wepdb.UserEmails.FirstOrDefault(w => w.Email.ToLower() == emailId.ToLower() && w.UserId == user.Id);
                    if (shouldCreateUserEmail)
                    {
                        userEmailExists = new UserEmail()
                        {
                            Id = Guid.NewGuid(),
                            UserId = userId,
                            AddedBy = userId.ToString(),
                            CreatedDate = DateTime.UtcNow,
                            DisplayName = user.FirstName + " " + user.LastName,
                            Email = emailId.ToLower(),
                            IsActive = true,
                            IsEmailValidated = false,
                            IsPersonal = true,
                            ModifiedDate = DateTime.UtcNow,
                            IsAccessible = false,
                            IsMultiUser = false,
                            IsGroupEmail = false,
                            Status = 0,
                            EmailType = 0,
                            IsPreviouslyOwned = false,
                            IsAddedByUser = true,
                            IsAcceptedByUser = true
                        };
                        wepdb.UserEmails.Add(userEmailExists);
                        wepdb.SaveChanges();
                    }
                    else
                    {
                        userEmailExists.IsActive = true;
                        wepdb.SaveChanges();
                    }
                    Guid orgID2 = CommonData.WhatElseCustomerOrgId;
                    bool isFree = false;
                    bool isProvisioned2 = true;
                    bool isTrail2 = false;
                    bool isAccepted2 = true;
                    bool isClaimed2 = false;
                    Guid provisionID = Guid.Empty;
                    userEmailExists.IsEmailValidated = true;
                    wepdb.SaveChanges();
                    var provisionExist = weodb.Provisions.Any(w => w.UserId == user.Id && w.EmailAddress == userEmailExists.Email && w.ProviderId == providerId);
                    if (provisionID == Guid.Empty && !provisionExist)
                    {
                        var userDomain2 = userEmailExists.Email.Split('@');
                        var domainName2 = userDomain2[1];
                        if (weddb.Domains.Any(w => w.Address == domainName2 && w.IsActive == true && w.IsFree == true) == true)
                        {
                            isFree = true;
                            isProvisioned2 = true;
                            isTrail2 = false;
                            isAccepted2 = true;
                            isClaimed2 = false;
                        }
                        var weOrgProvider = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == providerId && w.IsActive == true);
                        //var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive).ToList();

                        var isFreeDomain = weddb.FreeDomains.Any(w => w.DomainName.ToLower() == domainName2.ToLower());
                        if (isFreeDomain == false)
                        {
                            var orgExist2 = weodb.Orgs.Any(w => w.Domains == domainName2);
                            if (orgExist2 == true)
                            {
                                var org1 = weodb.Orgs.FirstOrDefault(w => w.Domains == domainName2);
                                orgID2 = org1.Id;
                                isFree = true;
                                isProvisioned2 = true;
                                isTrail2 = false;
                                isAccepted2 = true;
                                isClaimed2 = false;
                                var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w => w.OrgId == orgID2 && w.UserId == user.Id.ToString());
                                if (orgUserExist == null)
                                {
                                    var orgUser = new OrgUser();
                                    orgUser.Id = Guid.NewGuid();
                                    orgUser.IsActive = true;
                                    orgUser.CreatedDate = DateTime.UtcNow;
                                    orgUser.ActivatedOn = DateTime.UtcNow;
                                    orgUser.ModifiedDate = DateTime.UtcNow;
                                    orgUser.OrgId = orgID2;
                                    orgUser.UserId = user.Id;
                                    orgUser.IsAdmin = false;
                                    weodb.OrgUsers.Add(orgUser);
                                    weodb.SaveChanges();

                                    var orgDirectory = new OrgDirectory();
                                    orgDirectory.Id = Guid.NewGuid();
                                    orgDirectory.OrgId = orgID2;
                                    orgDirectory.CreatedDate = DateTime.UtcNow;
                                    orgDirectory.ModifiedDate = DateTime.UtcNow;
                                    orgDirectory.IsActive = true;
                                    orgDirectory.FirstName = user.FirstName ?? "";
                                    orgDirectory.MiddleName = user.MiddleName ?? "";
                                    orgDirectory.LastName = user.LastName ?? "";
                                    orgDirectory.SanitizedNumber = "";
                                    orgDirectory.ProvidedNumber = "";
                                    orgDirectory.Email = user.Email ?? "";
                                    orgDirectory.Designation = "";
                                    orgDirectory.Salutation = "";
                                    orgDirectory.UserId = user.Id.ToString();
                                    orgDirectory.CountryId = user.CountryId;
                                    weodb.OrgDirectories.Add(orgDirectory);
                                    weodb.SaveChanges();
                                }
                                var orgProviderExist2 = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == orgID2 && w.ProviderId == providerId && w.IsActive == true);
                                if (orgProviderExist2 == null)
                                {
                                    var orgProvider1 = new OrgProvider();
                                    orgProvider1.Id = Guid.NewGuid();
                                    orgProvider1.CreatedDate = DateTime.UtcNow;
                                    orgProvider1.ModifiedDate = DateTime.UtcNow;
                                    orgProvider1.IsActive = true;
                                    orgProvider1.OrgId = orgID2;
                                    orgProvider1.ProviderId = providerId;
                                    orgProvider1.CanSaveForOffline = true;
                                    orgProvider1.ForceCallLog = true;
                                    weodb.OrgProviders.Add(orgProvider1);
                                    weodb.SaveChanges();
                                    if (weOrgProvider != null)
                                    {
                                        var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();

                                        foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
                                        {
                                            var orgIdentifier = new OrgIdentifier();
                                            orgIdentifier.Id = Guid.NewGuid();
                                            orgIdentifier.OrgProviderId = orgProvider1.Id;
                                            orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
                                            orgIdentifier.IsActive = true;
                                            orgIdentifier.CreatedDate = DateTime.UtcNow;
                                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                            orgIdentifier.ShowSequence = 1;
                                            orgIdentifier.Value = weOrgProviderIdentifier.Value;
                                            weodb.OrgIdentifiers.Add(orgIdentifier);
                                            weodb.SaveChanges();
                                        }
                                    }
                                }
                            }
                            else
                            {
                                var Country = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
                                var org1 = new OnePage.Models.Models.OrgModel.Org();
                                org1.Id = Guid.NewGuid();
                                org1.IsBeta = false;
                                org1.AppId = CommonData.GlobalAppId.ToString();
                                // org1rg.IsPaid =;
                                org1.ModifiedDate = DateTime.UtcNow;
                                org1.CreatedDate = DateTime.UtcNow;
                                org1.AllowedAccounts = 1;
                                org1.Name = domainName2.ToUpper();
                                org1.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
                                org1.Countries = Country.Name + "|" + Country.Prefix;
                                org1.Domains = domainName2;
                                org1.IsActive = true;
                                org1.IsRestricted = false;
                                org1.IsProvider = false;
                                org1.IsPurchaser = false;
                                weodb.Orgs.Add(org1);
                                weodb.SaveChanges();
                                orgID2 = org1.Id;

                                var orgProvider1 = new OrgProvider();
                                orgProvider1.Id = Guid.NewGuid();
                                orgProvider1.CreatedDate = DateTime.UtcNow;
                                orgProvider1.ModifiedDate = DateTime.UtcNow;
                                orgProvider1.IsActive = true;
                                orgProvider1.OrgId = orgID2;
                                orgProvider1.ProviderId = providerId;
                                orgProvider1.CanSaveForOffline = true;
                                orgProvider1.ForceCallLog = true;
                                weodb.OrgProviders.Add(orgProvider1);
                                weodb.SaveChanges();
                                if (weOrgProvider != null)
                                {
                                    var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();
                                    foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
                                    {
                                        var orgIdentifier = new OrgIdentifier();
                                        orgIdentifier.Id = Guid.NewGuid();
                                        orgIdentifier.OrgProviderId = orgProvider1.Id;
                                        orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
                                        orgIdentifier.IsActive = true;
                                        orgIdentifier.CreatedDate = DateTime.UtcNow;
                                        orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                        orgIdentifier.ShowSequence = 1;
                                        orgIdentifier.Value = weOrgProviderIdentifier.Value;
                                        weodb.OrgIdentifiers.Add(orgIdentifier);
                                        weodb.SaveChanges();
                                    }
                                }
                            }
                        }
                        else
                        {
                            orgID2 = CommonData.WhatElseCustomerOrgId;
                        }
                        SetOrgLevelSettings(user.Id);
                        var provider1 = weddb.Providers.FirstOrDefault(w => w.Id == providerId && w.IsActive == true);
                        var existingProvision = weodb.Provisions.Any(w => w.AppId == user.AppId && w.EmailAddress == userEmailExists.Email
                        && w.ProviderId == providerId && w.OrgId == orgID2 && w.UserId == userId);
                        if (!existingProvision)
                        {
                            var provision1 = new Provision();
                            provision1.Id = Guid.NewGuid();
                            provision1.OrgId = orgID2; //CommonData.WhatElseCustomerOrgId;
                            provision1.AppId = user.AppId;
                            provision1.UserCustomerId = user.ChargebeeCustomerId;
                            provision1.ProviderTypeId = provider1.ProviderTypeId;
                            provision1.ProviderId = providerId;
                            provision1.UserId = user.Id;
                            provision1.UserProviderId = Guid.Empty;
                            provision1.CreatedDate = DateTime.UtcNow;
                            provision1.ModifiedDate = DateTime.UtcNow;
                            provision1.IsActive = true;
                            provision1.IsConverted = false;
                            provision1.IsEnterpriseConverted = false;
                            //provision.SanitizedNumber = user.SanitizedNumber;
                            provision1.IsRedeemed = false;
                            //provision.PhoneNumber = user.ProvidedNumber ?? "";
                            //provision.Salutation = user.Salutation;
                            provision1.FirstName = user.FirstName;
                            provision1.LastName = user.LastName;
                            provision1.MiddleName = user.MiddleName;
                            provision1.CountryId = user.CountryId;
                            provision1.UserId = user.Id;
                            provision1.IsFree = isFree;
                            provision1.IsProvisioned = isProvisioned2;
                            provision1.IsPayed = false;
                            provision1.IsRequested = false;
                            provision1.IsAccepted = isAccepted2;
                            provision1.IsPurchasedByUser = false;
                            provision1.IsPurchasedOnAndroid = false;
                            provision1.IsPurchasedOnIos = false;
                            provision1.EmailAddress = userEmailExists.Email;
                            provision1.IsClaimed = isClaimed2;
                            provision1.IsTrial = isTrail2;
                            weodb.Provisions.Add(provision1);
                            weodb.SaveChanges();

                            provision = provision1;
                        }
                    }
                    else
                    {
                        var provision1 = weodb.Provisions.First(w => w.UserId == user.Id && w.EmailAddress == userEmailExists.Email && w.ProviderId == providerId);
                        provision1.IsActive = true;
                        weodb.SaveChanges();
                        provision = provision1;
                    }
                }
                if (emailId.Contains(","))
                {
                    var mailIds = emailId.Split(',');
                    var emailExist = mailIds.Contains(provision.EmailAddress);
                    if (emailExist)
                    {
                        emailId = provision.EmailAddress;
                    }
                    else
                    {
                        //  return new Tuple<int, Guid>(3, userProviderId);
                    }
                }

                if (emailId.ToString().ToLower() == provision.EmailAddress.ToLower())
                {
                    //  var code = identifierList.FirstOrDefault(w => w.Name.Contains("code") && w.Value != null && w.Value != "").Value;
                    var userProvider = wepdb.UserProviders.FirstOrDefault(w => w.UserId == userId && w.EmailAddress == emailId.ToString().ToLower() && w.ProviderId == providerId && w.IsActive == true && w.AppId == user.AppId);
                    if (userProvider != null)
                    {
                        userProvider.ProvisionId = provision.Id;
                        wepdb.SaveChanges();
                    }
                    //if (userProvider != null)
                    //{
                    //TelegramService.SendMessageToTestBot("UserProvider Id: " + userProvider.Id.ToString());
                    TelegramService.SendMessageToTestBot("Provision Id: " + provision.Id.ToString());
                    var userProviderProvision = wepdb.UserProviders.FirstOrDefault(w => w.UserId == userId && w.ProvisionId == provision.Id && w.ProviderId == providerId && w.IsActive == true && w.EmailAddress == emailId.ToString().ToLower() && w.AppId == user.AppId);//

                    TelegramService.SendMessageToTestBot("UserProvider Provision: " + userProviderProvision.Id.ToString());
                    bool isMonthly = true;
                    DateTime start, end;
                    start = DateTime.UtcNow;
                    end = DateTime.UtcNow.AddDays(30);
                    if (provision.IsPayed == true)
                    {

                        var split = provision.ProductId.Split('.');
                        isMonthly = (split[3] == "m") ? true : false;
                        if (isMonthly == true)
                        {
                            start = DateTime.UtcNow;
                            end = DateTime.UtcNow.AddDays(30);
                        }
                        else
                        {
                            start = DateTime.UtcNow;
                            end = DateTime.UtcNow.AddYears(1);
                        }
                    }
                    if (userProvider != null || userProviderProvision != null)
                    {
                        var upid = (userProvider != null) ? userProvider.Id : (userProviderProvision != null) ? userProviderProvision.Id : Guid.Empty;
                        if (upid != Guid.Empty)
                        {
                            var up = wepdb.UserProviders.FirstOrDefault(w => w.Id == upid);
                            up.AppId = user.AppId;
                            up.OrgId = provision.OrgId;
                            up.ModifiedDate = DateTime.UtcNow;
                            up.ModifiedDate = DateTime.UtcNow;
                            up.EmailAddress = emailId.ToString().ToLower();
                            up.IsProvisioned = provision.IsProvisioned;
                            up.IsFree = provision.IsFree;
                            up.ProvisionId = provision.Id;
                            up.IsAuthenticated = true;
                            //userProvider.AccountId = 
                            up.IsActive = true;
                            up.Source = provision.Source ?? 0;
                            up.PurchaseId = provision.PurchaseId ?? "";
                            up.PurchaseState = provision.PurchaseState ?? 0;
                            up.ProductId = provision.ProductId ?? "";
                            up.IsRelogginRequired = false;
                            up.Priority = 100;
                            // wepdb.SaveChanges();
                            //wepdb.UserProviderIdentifiers.Where(w => w.UserProviderId == up.Id).ToList().ForEach(i => i.IsActive = false);

                            var identifiersFromRedisOrDb = await _userProviderCaching.GetUserProviderIdentifiersFromRedisOrDb(up.Id);
                            foreach (var x in identifiersFromRedisOrDb)
                            {
                                x.IsActive = false;
                                await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(x);

                            }


                            foreach (var identifier in identifierList)
                            {
                                try
                                {

                                    //var identifierExist = weddb.Identifiers.FirstOrDefault(w => w.Id.ToString() == UPIdentifierKeyValue.Key && w.ProviderId == providerId);
                                    if (identifier.Value != null && identifier.Value != "")
                                    {
                                        var upIdentifiers = new Models.Models.PeopleModel.UserProviderIdentifier();
                                        upIdentifiers.Id = Guid.NewGuid();
                                        upIdentifiers.IdentifierId = identifier.Id;
                                        upIdentifiers.UserProviderId = up.Id;
                                        upIdentifiers.Value = identifier.Value;
                                        upIdentifiers.IsActive = true;
                                        upIdentifiers.CreatedDate = DateTime.UtcNow;
                                        upIdentifiers.ModifiedDate = DateTime.UtcNow;
                                        // wepdb.UserProviderIdentifiers.Add(upIdentifiers);
                                        // wepdb.SaveChanges();
                                        await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(upIdentifiers);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    telemetryTracker.TrackException(ex, userId);
                                    continue;
                                    // throw;
                                }
                            }

                            var exsitingProvision = weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id);
                            exsitingProvision.IsRedeemed = true;
                            exsitingProvision.UserProviderId = up.Id;
                            exsitingProvision.ProviderId = providerId;
                            exsitingProvision.EmailAddress = emailId.ToLower();
                            exsitingProvision.IsTrial = Convert.ToBoolean(provision.IsTrial);
                            weodb.SaveChanges();
                        }
                    }
                    else
                    {
                        var newUserProvider = new Models.Models.PeopleModel.UserProvider();
                        newUserProvider.Id = Guid.NewGuid();
                        newUserProvider.UserId = userId;
                        newUserProvider.AppId = user.AppId;
                        newUserProvider.OrgId = provision.OrgId;
                        newUserProvider.ProviderId = providerId;
                        newUserProvider.EmailAddress = emailId.ToLower();
                        newUserProvider.IsAuthenticated = true;
                        newUserProvider.IsActive = true;
                        newUserProvider.Code = "";
                        newUserProvider.ActiveFrom = DateTime.UtcNow;
                        newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
                        newUserProvider.IsProvisioned = provision.IsProvisioned;
                        newUserProvider.ProvisionId = provision.Id;
                        newUserProvider.IsFree = provision.IsFree;
                        newUserProvider.IsFullSyncDone = false;
                        newUserProvider.Source = provision.Source ?? 0;
                        newUserProvider.PurchaseId = provision.PurchaseId ?? "";
                        newUserProvider.PurchaseState = provision.PurchaseState ?? 0;
                        newUserProvider.ProductId = provision.ProductId ?? "";
                        newUserProvider.CreatedDate = DateTime.UtcNow;
                        newUserProvider.ModifiedDate = DateTime.UtcNow;
                        newUserProvider.IsRelogginRequired = false;
                        newUserProvider.Priority = provider.ProviderType.Priority ?? 100;
                        int trialDays = 30;
                        newUserProvider.IsTrial = false;
                        // wepdb.UserProviders.Add(newUserProvider);
                        // wepdb.SaveChanges();
                        await _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider);

                        foreach (var identifier in identifierList)
                        {
                            try
                            {
                                if (identifier.Value != null && identifier.Value != "")
                                {

                                    var upIdentifiers = new Models.Models.PeopleModel.UserProviderIdentifier();
                                    upIdentifiers.Id = Guid.NewGuid();
                                    upIdentifiers.IdentifierId = identifier.Id;
                                    upIdentifiers.UserProviderId = newUserProvider.Id;
                                    upIdentifiers.Value = identifier.Value;
                                    upIdentifiers.IsActive = true;
                                    upIdentifiers.CreatedDate = DateTime.UtcNow;
                                    upIdentifiers.ModifiedDate = DateTime.UtcNow;
                                    // wepdb.UserProviderIdentifiers.Add(upIdentifiers);
                                    // wepdb.SaveChanges();
                                    await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(upIdentifiers);
                                }
                            }
                            catch (Exception ex)
                            {
                                telemetryTracker.TrackException(ex, userId);
                                continue;
                                // throw;
                            }
                        }
                        wepdb.SaveChanges();
                        var exsitingProvision = weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id);
                        exsitingProvision.IsRedeemed = true;
                        exsitingProvision.UserProviderId = newUserProvider.Id;
                        exsitingProvision.ProviderId = providerId;
                        exsitingProvision.EmailAddress = emailId.ToLower();
                        exsitingProvision.IsTrial = Convert.ToBoolean(provision.IsTrial);
                        weodb.SaveChanges();
                        //userProviderId = newUserProvider.Id;

                        //if (user.ChargebeeCustomerId != null)
                        //{
                        //    try
                        //    {
                        //        var msg = provider.Name + " LoggedIn-" + newUserProvider.EmailAddress;
                        //        ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                        //        EntityResult result = Customer.Retrieve(user.ChargebeeCustomerId).Request();
                        //        Customer customer = result.Customer;
                        //        if (customer != null)
                        //        {
                        //            EntityResult result1 = Comment.Create()
                        //               .EntityId(user.ChargebeeCustomerId)
                        //               .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                        //               .Notes(msg)
                        //               .Request();
                        //        }
                        //    }
                        //    catch (Exception ex)
                        //    {
                        //    }
                        //}
                    }

                    var userEmailExist = wepdb.UserEmails.FirstOrDefault(w => w.Email.ToLower() == emailId && w.UserId == userId);
                    if (userEmailExist == null && emailId != "")
                    {
                        var userEmail = new UserEmail();
                        userEmail.Id = Guid.NewGuid();
                        userEmail.Email = emailId ?? "";
                        userEmail.IsActive = true;
                        userEmail.UserId = userId;
                        userEmail.IsPersonal = true;
                        userEmail.CreatedDate = DateTime.UtcNow;
                        userEmail.ModifiedDate = DateTime.UtcNow;
                        userEmail.IsEmailValidated = true;
                        userEmail.IsAccessible = true;
                        userEmail.IsMultiUser = true;
                        userEmail.IsGroupEmail = false;
                        userEmail.Status = 1;
                        userEmail.IsAddedByUser = true;
                        userEmail.IsAcceptedByUser = false;
                        wepdb.UserEmails.Add(userEmail);
                    }
                    else
                    {
                        userEmailExist.IsEmailValidated = true;
                    }
                    wepdb.SaveChanges();
                    if (provision.IsProvisioned == true)// && provision.OrgId != CommonData.WhatElseOrgId)
                    {
                        var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w => w.OrgId == provision.OrgId && w.UserId == userId.ToString());
                        if (orgUserExist == null)
                        {
                            var orgUser = new OrgUser();
                            orgUser.Id = Guid.NewGuid();
                            orgUser.IsActive = true;
                            orgUser.CreatedDate = DateTime.UtcNow;
                            // orgUser.DeactivatedOn =;
                            orgUser.ActivatedOn = DateTime.UtcNow;
                            orgUser.ModifiedDate = DateTime.UtcNow;
                            orgUser.OrgId = provision.OrgId;
                            orgUser.UserId = userId;
                            orgUser.IsAdmin = false;
                            weodb.OrgUsers.Add(orgUser);
                            weodb.SaveChanges();

                            var orgDirectory = new OrgDirectory();
                            orgDirectory.Id = Guid.NewGuid();
                            orgDirectory.OrgId = provision.OrgId;
                            orgDirectory.CreatedDate = DateTime.UtcNow;
                            orgDirectory.ModifiedDate = DateTime.UtcNow;
                            orgDirectory.IsActive = true;
                            orgDirectory.FirstName = user.FirstName ?? "";
                            orgDirectory.MiddleName = user.MiddleName ?? "";
                            orgDirectory.LastName = user.LastName ?? "";
                            orgDirectory.Email = user.Email ?? "";
                            orgDirectory.SanitizedNumber = "";
                            orgDirectory.ProvidedNumber = "";
                            orgDirectory.UserId = user.Id.ToString();
                            orgDirectory.CountryId = user.CountryId;
                            weodb.OrgDirectories.Add(orgDirectory);
                            weodb.SaveChanges();
                            weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id).OrgDirectoryId = orgDirectory.Id;
                            weodb.SaveChanges();
                        }
                    }
                    wepdb.SaveChanges();
                    weodb.SaveChanges();
                    weadb.SaveChanges();
                    //}
                }

                var split1 = userData.Email.Split('@');
                var orgData = await weodb.Orgs.AsNoTracking().FirstOrDefaultAsync(w => w.Name.ToLower() == split1[1].ToLower() && w.AppId == CommonData.GlobalAppId.ToString().ToLower());

                //   var OrgId = OrgUsers.Where(x => x.Org.Name.ToLower() == split1[1].ToLower() && x.Org.AppId.ToLower() == CommonData.GlobalAppId.ToString().ToLower()).Select(x => x.OrgId).FirstOrDefault();
                var org2 = weodb.Orgs.FirstOrDefault(w => w.Id == orgData.Id);
                string redirectUrl = "";

                if (AppId == CommonData.GlobalAppId)
                {
                    if (org2.IsPaid == true)
                        redirectUrl = await weadb.DeploymentUrls.AsNoTracking().Where(w => w.IsPaid == org2.IsPaid && w.OrgId == orgData.Id && w.ServerType == 2).Select(x => x.RedirectUrl).FirstOrDefaultAsync();
                    else
                        redirectUrl = weadb.DeploymentUrls.FirstOrDefault(w => w.IsPaid == false && w.OrgId == CommonData.WhatElseCustomerOrgId && w.ServerType == 2).RedirectUrl;
                }

                Djson4.RedirectUrl = redirectUrl;
                Djson4.DeviceId = dId1;
                var domain = split1[1];
                var primaryOrg = weodb.Orgs.FirstOrDefault(w => w.AppId.ToString().ToLower() == userData.AppId.ToString().ToLower() && w.Name.ToLower().Contains(domain.ToLower()));
                if (primaryOrg != null)
                    Djson4.PrimaryOrgId = primaryOrg.Id;
                var checkForCalendarSettingId = Guid.Parse("1D9BC314-786C-40E4-9FE4-769860C58F12");
                var userSettings = wepdb.UserSettings.Where(w => w.UserId == userId && w.SettingId == checkForCalendarSettingId).FirstOrDefault();
                if (userSettings != null)
                {
                    Djson4.IsCalendarConnected = userSettings.Value == "0" ? false : true;
                }
                return Ok(Djson4);
                // return new Tuple<int, Guid>(1, userProviderId);
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot2("sso api error");
                TelegramService.SendMessageToTestBot2(ex.Message.ToString());
                TelegramService.SendMessageToTestBot2(ex.StackTrace.ToString());
                //  TelegramService.SendMessageToTestBot2("internal exception : " + ex.InnerException.Message);
                TelegramService.SendMessageToTestBot2(ex.ToString());
                telemetryTracker.TrackException(ex, Guid.Empty);
                return BadRequest(ex.ToString());
            }
        }
        public async Task<PreloginReturnModel2> ProcessServerZoneLogin(AuthUserModel userModel)
        {
            try
            {
                var appid = (userModel.AppId == Guid.Empty) ? CommonData.GlobalAppId : userModel.AppId;
                var tempPassword = "123456";
                var email = userModel.EmailAddress.ToLower().TrimStart().TrimEnd();

                User user = wepdb.Users.FirstOrDefault(w => w.Email == email && w.IsUser && w.AppId == userModel.AppId);

                var appInfo = weadb.Apps.FirstOrDefault(w => w.Id == userModel.AppId && w.IsActive);
                var androidOneSignalId = appInfo.AndroidOneSignalId;
                var androidOneSignalKey = appInfo.AndroidOneSignalApiKey;
                var IosOneSignalId = appInfo.IosoneSignalId;
                var IosOneSignalKey = appInfo.IosoneSignalApiKey;
                var WebOneSignalId = appInfo.WebOneSignalId;
                var WebOneSignalKey = appInfo.WebOneSignalApiKey;
                var supportEmail = appInfo.SupportEmail;
                var supportPhone = (userModel.AppId != CommonData.GlobalAppId) ? "" : (appInfo.SupportPhone ?? "");
                var supportName = appInfo.SupportName;
                var authUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 17 && w.AppId.ToString().ToLower() == userModel.AppId.ToString().ToLower() && w.IsActive).Url;
                PreloginReturnModel2 preloginReturnModel = new PreloginReturnModel2();
                var domainName = email.Split('@');
                string deviceType = Common.CommonData.GetDeviceType(userModel.DeviceType);
                string loginFlow = CommonData.GetLoginFlow(userModel.LoginFlowId);
                bool istestuser = false;
                if (domainName.Length > 1)
                {
                    istestuser = CommonData.testUserDomains.Contains(domainName[1]);
                }
                if (null != user)
                {
                    //  await checkAndApplyForCoupon(user);
                    //   if (user.IsTestUser == false && !userModel.EmailAddress.ToLower().Contains("onmicrosoft.com"))
                    if (user.IsTestUser == false)
                    {
                        botService.SendMessageForExistingUsers(user.Email, user.FirstName + " " + user.LastName, deviceType, loginFlow);
                        //TelegramService.SendMessage("Existing user trying to login: " + userModel.EmailAddress + "\r\n" + "Device Type: " + deviceType + "\r\n" + "Login: " + loginFlow);
                    }
                    var appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == user.AppId && w.LanguageId == user.LanguageId);
                    AppLanguageTemplate appSendgridTemplateId = new AppLanguageTemplate();
                    // if (appLanguage == null)
                    // {
                    //     appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == CommonData.GlobalAppId && w.LanguageId == 1);
                    //     appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail");
                    // }
                    // else
                    // {
                    //     appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail");
                    // }
                    var userDevices = wepdb.UserDevices.Where(w => w.UserId == user.Id && w.IsActive == true).ToList();
                    bool loginAllowed = (appInfo.IsOneDevicePerAccount == true) ? ((userDevices.Count > 0) ? false : true) : true;
                    bool IsEmailValid = false;
                    if (istestuser)
                    {
                        loginAllowed = true;
                        IsEmailValid = true;
                    }

                    if (user.IsDisabled == true)
                    {
                        preloginReturnModel.UserType = 2;
                        preloginReturnModel.AuthUrl = "";
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = loginAllowed;
                        preloginReturnModel.IsEmailValid = IsEmailValid;
                        preloginReturnModel.NotificarLoginBlocked = false;
                        preloginReturnModel.LoginType = 0;
                        return preloginReturnModel;
                    }
                    if (string.IsNullOrEmpty(user.CountryId))
                    {
                        user.CountryId = userModel.CountryId ?? "IN";
                        wepdb.SaveChanges();
                    }
                    if (userModel.IsPreAuthenticated)
                    {
                        user.Password = userModel.Password + "|1";
                        wepdb.SaveChanges();

                        preloginReturnModel.UserType = 0;
                        preloginReturnModel.AuthUrl = authUrl;
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = loginAllowed;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.NotificarLoginBlocked = false;
                        preloginReturnModel.IsPreAuthenticated = true;
                        preloginReturnModel.LoginType = 0;
                        return preloginReturnModel;
                    }

                    preloginReturnModel.UserType = 0;
                    preloginReturnModel.AuthUrl = authUrl;
                    preloginReturnModel.SupportEmail = supportEmail;
                    preloginReturnModel.SupportPhone = supportPhone;
                    preloginReturnModel.IsLoginAllowed = loginAllowed;
                    preloginReturnModel.NotificarLoginBlocked = false;
                    preloginReturnModel.LoginType = 0;
                    return preloginReturnModel; // Existing User

                }
                else
                {
                    //  if (!userModel.EmailAddress.ToLower().Contains("onmicrosoft.com"))
                    if (userModel.AppId == CommonData.OPMaukaAppId)
                    {
                        botService.SendMessageForNewOppNewUsers(userModel.EmailAddress, "", deviceType, loginFlow);
                        // TelegramService.SendMessageToMoukhaBot("New user trying to login: " + userModel.EmailAddress + "\r\n" + "Device Type: " + deviceType + "\r\n" + "Login: " + loginFlow);
                    }
                    else
                    {
                        botService.SendMessageForNewUsers(userModel.EmailAddress, "", deviceType, loginFlow);
                        // TelegramService.SendMessage("New user trying to login: " + userModel.EmailAddress + "\r\n" + "Device Type: " + deviceType + "\r\n" + "Login: " + loginFlow);
                    }
                    if (userModel.AppId != Common.CommonData.OPAppId && userModel.AppId != Common.CommonData.OPV2AppId && userModel.AppId != CommonData.GlobalAppId)
                    {
                        preloginReturnModel.UserType = 4;
                        preloginReturnModel.AuthUrl = "";
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = false;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.NotificarLoginBlocked = true;
                        preloginReturnModel.LoginType = 3;
                        return preloginReturnModel;
                    }
                    if (userModel.IsPreAuthenticated)
                    {
                        Guid userId = Guid.NewGuid();
                        user = new User()
                        {
                            // SanitizedNumber = santizedPhoneNumber,
                            // PType = Int32.Parse(),
                            Id = userId,
                            Password = userModel.Password + "|1",
                            CountryId = userModel.CountryId ?? "IN",
                            //ProvidedNumber = userModel.PhoneNumber,
                            LastLogin = DateTime.UtcNow,
                            PreviousLogin = DateTime.UtcNow,
                            FirstLogin = DateTime.UtcNow,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            FirstName = userModel.FirstName,
                            LastName = userModel.LastName,
                            IsDisabled = false,
                            IsUser = true,
                            IsActive = true,
                            Notes = "",
                            AppId = userModel.AppId,
                            LanguageId = 1,
                            Email = email,
                            IsEmailValidated = false,
                            //IsPhoneValidated = false,
                            AnalyticsId = Guid.NewGuid(),
                            IsTestUser = istestuser
                        };
                        wepdb.Users.Add(user);
                        wepdb.SaveChanges();

                        AuthUserModel3 userModel3 = new AuthUserModel3
                        {
                            AppId = userModel.AppId,
                            CountryId = userModel.CountryId,
                            Coupon = userModel.Coupon,
                            EmailAddress = userModel.EmailAddress,
                            PhoneNumber = userModel.PhoneNumber
                        };

                        AddAdditionalTableDataUsingEmail(userModel3, email, userId, supportEmail, supportPhone, supportName);
                        preloginReturnModel.UserType = 1;
                        preloginReturnModel.AuthUrl = authUrl;
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = true;
                        preloginReturnModel.NotificarLoginBlocked = true;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.LoginType = 0;
                        return preloginReturnModel; // New User 
                    }
                    else
                    {
                        if (userModel == null)
                            TelegramService.SendMessageToTestBot2("usermodel null");
                        TelegramService.SendMessageToTestBot2(userModel.EmailAddress);
                        bool emailWhiteListExist = wepdb.EmailWhiteLists.Any(w => w.Email.ToLower() == userModel.EmailAddress.ToLower() && w.IsActive == true);
                        var domain = userModel.EmailAddress.Split('@');
                        if (!emailWhiteListExist && (domain.Count() > 0) && (wepdb.DomainBlacklists.Select(W => W.DomainName.ToLower()).Contains(domain[1].ToLower())))
                        {
                            preloginReturnModel.UserType = 3;
                            preloginReturnModel.AuthUrl = "";
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = false;
                            preloginReturnModel.IsEmailValid = true;
                            preloginReturnModel.NotificarLoginBlocked = true;
                            preloginReturnModel.LoginType = 4;
                            return preloginReturnModel;
                        }
                        var isValidEmail = await VerifyEmailByKickBox(userModel.EmailAddress);
                        //  var isValidEmail = await IsValidEmail(userModel.EmailAddress);
                        if (isValidEmail == true || emailWhiteListExist == true || istestuser == true)
                        {
                            if (!(istestuser || emailWhiteListExist))
                            {
                                if (!wepdb.EmailWhiteLists.Any(w => w.Email == userModel.EmailAddress.ToLower()))
                                {
                                    EmailWhiteList emailWhite = new EmailWhiteList
                                    {
                                        Email = userModel.EmailAddress.ToLower(),
                                        IsActive = false,
                                        Id = Guid.NewGuid()
                                    };
                                    wepdb.EmailWhiteLists.Add(emailWhite);
                                    wepdb.SaveChanges();
                                }

                                preloginReturnModel.UserType = 2;
                                preloginReturnModel.AuthUrl = authUrl;
                                preloginReturnModel.SupportEmail = supportEmail;
                                preloginReturnModel.SupportPhone = supportPhone;
                                preloginReturnModel.IsLoginAllowed = false;
                                preloginReturnModel.IsEmailValid = istestuser ? true : false;
                                preloginReturnModel.NotificarLoginBlocked = true;
                                preloginReturnModel.LoginType = 0;
                                return preloginReturnModel;
                                //TODO: send email to admin
                            }

                            if (userModel.AppId == CommonData.MiofertaAppId || userModel.AppId == CommonData.NotificarAWSTestApp)
                            {
                                preloginReturnModel.UserType = 1;
                                preloginReturnModel.AuthUrl = "";
                                preloginReturnModel.SupportEmail = supportEmail;
                                preloginReturnModel.SupportPhone = supportPhone;
                                preloginReturnModel.IsLoginAllowed = true;
                                preloginReturnModel.NotificarLoginBlocked = true;
                                preloginReturnModel.LoginType = 0;
                                return preloginReturnModel;
                            }
                            Guid userId = Guid.NewGuid();
                            if (istestuser)
                            {
                                var encriptedPwd = CommonData.passwordEncryption(tempPassword);
                                tempPassword = encriptedPwd;
                            }
                            else
                            {
                                var appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == userModel.AppId && w.LanguageId == 1);
                                var appSendgridTemplateId = "";
                                if (appLanguage == null)
                                {
                                    appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == CommonData.GlobalAppId && w.LanguageId == 1);
                                    appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail").TemplateId;
                                }
                                else
                                {
                                    appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail").TemplateId;
                                }
                                var random = new Random();
                                var otp = random.Next(100000, 999999);
                                var encriptedPwd = CommonData.passwordEncryption(otp.ToString());
                                tempPassword = encriptedPwd;
                                var smsMessage = string.Format("Your one time code {0} to complete phone number verification.You have agreed to the terms and conditions at " + appInfo.Tocurl, otp);

                                var res = EmailServices.SendGridOTPMail(true, "", otp.ToString(), userModel.EmailAddress, userModel.EmailAddress);
                            }
                            user = new User()
                            {
                                // SanitizedNumber = santizedPhoneNumber,
                                // PType = Int32.Parse(),
                                Id = userId,
                                Password = tempPassword,
                                CountryId = userModel.CountryId ?? "IN",
                                //ProvidedNumber = userModel.PhoneNumber,
                                LastLogin = DateTime.UtcNow,
                                PreviousLogin = DateTime.UtcNow,
                                FirstLogin = DateTime.UtcNow,
                                CreatedDate = DateTime.UtcNow,
                                ModifiedDate = DateTime.UtcNow,
                                IsDisabled = false,
                                IsUser = true,
                                IsActive = true,
                                Notes = "",
                                AppId = userModel.AppId,
                                LanguageId = 1,
                                Email = email,
                                IsEmailValidated = false,
                                //IsPhoneValidated = false,
                                AnalyticsId = Guid.NewGuid(),
                                IsTestUser = istestuser
                            };

                            wepdb.Users.Add(user);
                            wepdb.SaveChanges();
                            var couponData = wepdb.Coupons.FirstOrDefault(w => w.UserId.ToString().StartsWith(userModel.Coupon) || w.Id.ToString().Contains(userModel.Coupon) && w.IsCancelled == false);
                            AuthUserModel3 userModel3 = new AuthUserModel3
                            {
                                AppId = userModel.AppId,
                                CountryId = userModel.CountryId,
                                Coupon = userModel.Coupon,
                                EmailAddress = userModel.EmailAddress,
                                PhoneNumber = userModel.PhoneNumber
                            };

                            AddAdditionalTableDataUsingEmail(userModel3, email, userId, supportEmail, supportPhone, supportName);
                            preloginReturnModel.UserType = 1;
                            preloginReturnModel.AuthUrl = authUrl;
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = true;
                            preloginReturnModel.IsEmailValid = true;
                            preloginReturnModel.NotificarLoginBlocked = true;
                            preloginReturnModel.LoginType = 0;
                            return preloginReturnModel; // New User 
                        }
                        else
                        {
                            preloginReturnModel.UserType = 4;
                            preloginReturnModel.AuthUrl = "";
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = false;
                            preloginReturnModel.IsEmailValid = false;
                            preloginReturnModel.NotificarLoginBlocked = true;
                            preloginReturnModel.LoginType = 3;
                            return preloginReturnModel;
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, Guid.Empty);
                TelegramService.SendMessageToTestBot2(ex.ToString());
                TelegramService.SendMessageToTestBot(ex.StackTrace.ToString());
                return null;
            }
        }
        async Task<bool> VerifyEmailByKickBox(string email)
        {
            try
            {
                sbnew.AppendLine("New UserEmail: " + email);
                var replaceEmail = CommonData.KickboxApi.Replace("{emailaddress}", email);
                var finalApi = replaceEmail.Replace("{kickboxkey}", CommonData.KickboxApiKey);

                using (HttpClient httpClient = new HttpClient())
                {
                    HttpRequestMessage requestMessage = new HttpRequestMessage(System.Net.Http.HttpMethod.Get, finalApi);
                    HttpResponseMessage response = await httpClient.SendAsync(requestMessage);
                    string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                    JToken resultData = JsonConvert.DeserializeObject<JToken>(content);
                    var result = resultData.SelectToken("$.result").Value<string>();
                    sbnew.AppendLine("result: " + result);
                    var desposable = resultData.SelectToken("$.disposable").Value<bool>();
                    sbnew.AppendLine("desposable: " + desposable);
                    var sendex = resultData.SelectToken("$.sendex").Value<double>();
                    sbnew.AppendLine("sendex: " + sendex);
                    var success = resultData.SelectToken("$.success").Value<bool>();
                    sbnew.AppendLine("success: " + success);
                    if (desposable == false && sendex > 0.4 && success == true)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }

            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, Guid.Empty);
                return false;
            }
        }
        private void AddAdditionalTableDataUsingEmail(AuthUserModel3 userModel, string email, Guid userId, string supportEmail, string supportPhone, string supportName)
        {
            try
            {
                bool shouldShowAds = (userModel.AppId == CommonData.GlobalAppId) ? true : false;

                User user = _redisCaching.CheckForItem("userDetail_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddDays(1));

                Models.Models.PeopleModel.UserStatus userStatus = new Models.Models.PeopleModel.UserStatus()
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    AndroidContacts = false,
                    FacebookContacts = false,
                    FreeEmail = false,
                    FreeLinkedIn = false,
                    FreeFacebook = false,
                    FreeStorage = false,
                    FreeTwitter = false,
                    IOscontacts = false,
                    LinkedInContacts = false,
                    MobileVerification = false,
                    Otp = false,
                    ProvisionApp = false,
                    PurchaseEmail = false,
                    PurchaseEnterpriseApp = false,
                    PurchaseEnterpriseEmail = false,
                    PurchaseEnterpriseStorage = false,
                    PurchaseStorage = false,
                    Registration = false,
                    TwitterContacts = false,
                    Walkthrough = false,
                    ShouldShowAds = shouldShowAds
                };
                wepdb.UserStatuses.Add(userStatus);
                wepdb.SaveChanges();
                var deviceContactProviders = weddb.Providers.Where(w => w.ProviderTypeId == CommonData.DeviceContactsProviderTyepId).ToList();
                foreach (var deviceContactProvider in deviceContactProviders)
                {
                    if (deviceContactProvider.Id != CommonData.ContactAddedByAdmin && deviceContactProvider.Id != CommonData.ManualProviderId)
                    {
                        var provision1 = new Provision();
                        provision1.Id = Guid.NewGuid();
                        provision1.OrgId = CommonData.WhatElseCustomerOrgId;
                        provision1.UserCustomerId = user.ChargebeeCustomerId;
                        provision1.AppId = user.AppId;
                        provision1.ProviderTypeId = deviceContactProvider.ProviderTypeId;
                        provision1.ProviderId = deviceContactProvider.Id;
                        provision1.UserId = user.Id;
                        provision1.UserProviderId = Guid.Empty;
                        provision1.CreatedDate = DateTime.UtcNow;
                        provision1.ModifiedDate = DateTime.UtcNow;
                        provision1.IsActive = true;
                        provision1.IsConverted = false;
                        provision1.IsEnterpriseConverted = false;
                        // provision1.SanitizedNumber = user.SanitizedNumber;
                        provision1.IsRedeemed = false;
                        provision1.PhoneNumber = "";
                        //provision1.Salutation = user.Salutation;
                        provision1.FirstName = user.FirstName ?? "";
                        provision1.LastName = user.LastName ?? "";
                        provision1.MiddleName = user.MiddleName ?? "";
                        //  provision1.CountryId = user.CountryId;
                        provision1.UserId = user.Id;
                        provision1.IsFree = false;
                        provision1.IsProvisioned = true;
                        provision1.IsPayed = false;
                        provision1.IsRequested = false;
                        provision1.IsAccepted = false;
                        provision1.IsPurchasedByUser = false;
                        provision1.IsPurchasedOnAndroid = false;
                        provision1.IsPurchasedOnIos = false;
                        provision1.EmailAddress = deviceContactProvider.InShort;
                        provision1.IsClaimed = true;
                        provision1.IsTrial = false;
                        weodb.Provisions.Add(provision1);
                        weodb.SaveChanges();
                        var newUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                        {
                            Id = Guid.NewGuid(),
                            UserId = userId,
                            AppId = user.AppId,
                            OrgId = CommonData.WhatElseCustomerOrgId,
                            Status = (int)UserProviderStatus.NewProvider,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            ProviderId = deviceContactProvider.Id,
                            IsActive = true,
                            ActiveFrom = DateTime.UtcNow,
                            ActiveTill = DateTime.UtcNow.AddYears(10),
                            ProvisionId = provision1.Id,
                            EmailAddress = deviceContactProvider.InShort
                        };
                        // wepdb.UserProviders.Add(newUserProvider1);
                        // wepdb.SaveChanges();
                        _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider1).GetAwaiter().GetResult();
                        provision1.UserProviderId = newUserProvider1.Id;
                        weodb.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);

            }
        }
        public async Task<SSOReturnModel> ProcessValidateApi(ValidationModel validationModel)
        {
            try
            {
                var email = validationModel.EmailAddress.ToLower().TrimStart().TrimEnd();
                var languageIdExist = CommonData.languageList.Any(w => w.Name == validationModel.LanguageId);
                if (email == null && languageIdExist == false)
                    return null;
                var dTypeId = validationModel.DeviceTypeId.ToString().ToLower();
                User existingUser = wepdb.Users.FirstOrDefault(w => w.Email == email && w.IsUser && w.AppId == validationModel.AppId);

                if (null != existingUser)
                {
                    var deviceType = weddb.DeviceTypes.FirstOrDefault(w => w.Id == validationModel.DeviceTypeId);

                    bool isTestUser = (bool)existingUser.IsTestUser;
                    SetUserSettings(existingUser.Id);
                    try
                    {
                        TelegramService.SendMessageToTestBot2("userId:  " + existingUser.Id.ToString());
                    }
                    catch (Exception ex)
                    {
                        TelegramService.SendMessageToTestBot2(ex.ToString());
                    }
                    //  await CheckForSubscription(existingUser.Id);
                    var returnModel = new SSOReturnModel();
                    wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id).Otp = true;
                    wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id).Walkthrough = true;
                    wepdb.SaveChanges();
                    UserStatusModel userStatusData = wepdb.UserStatuses.Where(w => w.UserId == existingUser.Id).Select(w => new UserStatusModel()
                    {
                        Id = w.Id,
                        AndroidContacts = w.AndroidContacts,
                        FacebookContacts = w.FacebookContacts,
                        FreeEmail = w.FreeEmail,
                        FreeLinkedIn = w.FreeLinkedIn,
                        FreeStorage = w.FreeStorage,
                        iOSContacts = w.IOscontacts,
                        FreeTwitter = w.FreeTwitter,
                        LinkedInContacts = w.LinkedInContacts,
                        MobileVerification = w.MobileVerification,
                        OTP = w.Otp,
                        ProvisionApp = w.ProvisionApp,
                        PurchaseEmail = w.PurchaseEmail,
                        PurchaseEnterpriseApp = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseEmail = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseStorage = w.PurchaseEnterpriseStorage,
                        PurchaseStorage = w.PurchaseStorage,
                        Registration = w.Registration,
                        TwitterContacts = w.TwitterContacts,
                        Walkthrough = w.Walkthrough,
                        ShouldShowAds = (bool)w.ShouldShowAds,
                    }).FirstOrDefault();
                    existingUser.PreviousLogin = existingUser.LastLogin;
                    //existingUser.IsPhoneValidated = false;
                    existingUser.IsEmailValidated = true;
                    existingUser.LastLogin = DateTime.UtcNow;
                    existingUser.ModifiedDate = DateTime.UtcNow;
                    existingUser.IsUser = true;
                    if (existingUser.FirstLogin == null)
                        existingUser.FirstLogin = DateTime.UtcNow;
                    Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        UserDeviceId = validationModel.DeviceId
                    };
                    Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = true
                    };
                    Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Details = "Webhook",
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = false
                    };
                    wetdb.Tokens.Add(webtoken);
                    wetdb.Tokens.Add(webhooktoken);
                    wetdb.Tokens.Add(token);
                    wetdb.SaveChanges();
                    wepdb.SaveChanges();
                    //Task.Run(() => UpdateOtherTablesUsingEmail(validationModel, email, existingUser));
                    UpdateOtherTablesUsingEmail(validationModel, email, existingUser, token);

                    // int notificationCount = db.MergeContacts.Count(w => w.OtherUserId == existingUser.Id && w.IsActive);
                    var domainName = existingUser.Email.Split('@');
                    var domain = domainName[1];

                    var calendarUserProviderExists = wepdb.UserProviders.Any(w => w.UserId == existingUser.Id && (w.ProviderId == CommonData.MicrosoftProviderId || w.ProviderId == CommonData.GoogleCalendarProviderId || w.ProviderId == CommonData.ZohoCalendarProviderId || w.ProviderId == CommonData.CalendlyProviderId || w.ProviderId == CommonData.CalendarHeroProviderId) && w.IsActive == true);
                    var suiteUserProviderExists = await wepdb.UserProviders.AsNoTracking().AnyAsync(x => x.UserId == existingUser.Id && (x.ProviderId == CommonData.GoogleSuiteProviderId || x.ProviderId == CommonData.MicrosoftSuiteProviderId) && x.IsActive == true);

                    var orgDetail = await weodb.Orgs
                      .Where(x => x.AppId.ToLower() == existingUser.AppId.ToString().ToLower() &&
                                  x.IsActive == true && x.Name.ToLower() == domain.ToLower()).FirstOrDefaultAsync();
                    if (orgDetail.IsPaid == true)
                    {
                        returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                        returnModel.WebOneSignalKey = orgDetail.WebOneSignalKey;
                    }
                    else
                    {

                        orgDetail = weodb.Orgs.Where(x => x.Id == CommonData.WhatElseCustomerOrgId).FirstOrDefault();
                        returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                    }
                    var isUserCouponsExists = await wepdb.UserCoupons.AsNoTracking().AnyAsync(w => w.EmailId.ToLower() == existingUser.Email.ToLower() && w.IsApplied == true && w.IsActive == true);
                    returnModel.WebhookToken = webhooktoken.Id;
                    returnModel.IsAppSumoUser = isUserCouponsExists;
                    returnModel.ServerUrlModel = await weadb.Servers.AsNoTracking().Where(w => w.IsActive && w.AppId == existingUser.AppId).Select(w => new ServerUrlModel()
                    {
                        ServerType = w.ServerType,
                        url = w.Url
                    }).ToListAsync();
                    if (dTypeId == "467aae32-8361-43a3-a8c7-be21e32c4f3b")
                    {
                        returnModel.IsTeamsTrial = true;
                        returnModel.IsWebTrial = false;
                    }
                    else if (dTypeId == "9c329792-9519-4d6c-be9d-f12898113416")
                    {
                        returnModel.IsTeamsTrial = false;
                        returnModel.IsWebTrial = false;
                    }
                    returnModel.IsPaidUser = (bool)orgDetail.IsPaid;
                    returnModel.TokenId = token.Id;
                    returnModel.UserId = existingUser.Id;
                    returnModel.UserStatusModel = userStatusData;
                    returnModel.UserContactId = Guid.Empty;
                    returnModel.NotificationCount = 0;//notificationCount;
                    returnModel.LanguageId = validationModel.LanguageId;
                    returnModel.UnknownPush = false;
                    returnModel.ContactPush = false;
                    returnModel.CRMPush = false;
                    returnModel.DirectoryPush = false;
                    returnModel.IsTestUser = isTestUser;
                    returnModel.FirstName = existingUser.FirstName;
                    returnModel.LastName = existingUser.LastName;
                    returnModel.AnalyticsId = existingUser.AnalyticsId;
                    returnModel.WebToken = webtoken.Id;
                    returnModel.WebhookToken = webhooktoken.Id;
                    returnModel.InvitedBy = existingUser.InvitedBy;
                    returnModel.CallNotifications = false;
                    returnModel.EmailAddress = existingUser.Email;
                    returnModel.IsInternal = false;
                    returnModel.UserLinkedinUrl = existingUser.LinkedinUrl;
                    returnModel.IsCalendarConnected = calendarUserProviderExists;
                    returnModel.IsSuiteProviderConnected = suiteUserProviderExists;
                    try
                    {
                        var orguser = await weodb.OrgUsers.AsNoTracking().Where(w => w.UserId == existingUser.Id && w.Org.Name == domain && w.Org.IsInternal).FirstOrDefaultAsync();
                        if (orguser != null)
                            returnModel.IsInternal = orguser.Org.IsInternal;
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex, Guid.Empty);
                        Console.WriteLine(ex.ToString());
                    }
                    var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == domain.ToLower());
                    returnModel.OrgIds = await weodb.OrgUsers.AsNoTracking().Where(w => w.UserId == existingUser.Id && w.IsActive).Select(w => w.OrgId).Distinct().ToListAsync();
                    returnModel.IsFreeDomainUser = isFreeDomain;
                    returnModel.CalendarNotifications = true;
                    returnModel.ServerUrlModel = await weadb.Servers.AsNoTracking().Where(w => w.IsActive && w.AppId == existingUser.AppId).Select(w => new ServerUrlModel()
                    {
                        ServerType = w.ServerType,
                        url = w.Url
                    }).ToListAsync();
                    return returnModel;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot2(ex.ToString());
                TelegramService.SendMessageToTestBot2(ex.StackTrace);
                TelegramService.SendMessageToTestBot2("internal exception : " + ex.InnerException.Message);
                telemetryTracker.TrackException(ex, Guid.Empty);
                return null;
            }
        }
        private void UpdateOtherTablesUsingEmail(ValidationModel validationModel, string email, User user, Models.Models.TokenModel.Token token)
        {
            try
            {
                DataContext we = weddb;
                //wesdb = new WESyncEntities();
                OrgContext weo = weodb;
                PeopleContext wepdbs = wepdb;
                var existingUser = wepdbs.Users.FirstOrDefault(w => w.Id == user.Id);

                bool isPrimary = (validationModel.DeviceTypeId == CommonData.IOSDeviceTypeId) ? false : true;

                // to set user device and token to fasle if user logs in same device without uninstalling
                if (validationModel.DeviceData != "")
                {
                    var userDevices = wepdbs.UserDevices.Where(w => w.UserId == existingUser.Id && w.IsActive == true && w.DeviceData == validationModel.DeviceData).ToList();
                    if (userDevices.Count() > 0)
                    {
                        foreach (var userDeviceItem in userDevices)
                        {
                            userDeviceItem.IsActive = false;
                            wepdbs.SaveChanges();
                            var activeToken = wetdb.Tokens.FirstOrDefault(w => w.UserDeviceId == userDeviceItem.Id && w.IsActive == true);
                            if (activeToken != null)
                            {
                                activeToken.IsActive = false;
                                wetdb.SaveChanges();
                            }
                        }
                    }
                }
                if (!wepdbs.UserDevices.Any(x => x.Id == validationModel.DeviceId))
                {
                    UserDevice userDevice = new UserDevice()
                    {
                        Id = validationModel.DeviceId,
                        DeviceTypeId = validationModel.DeviceTypeId,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        //CountryId = countryId,
                        UserId = existingUser.Id,
                        IsPhoneValidated = false,
                        //SanitizedNumber = santizedPhoneNumber,
                        IsPrimary = isPrimary,
                        IsPersonal = true,
                        //PhoneNumber = providedNumber,
                        //PType = Int32.Parse(numberType),
                        DeviceData = validationModel.DeviceData ?? "",
                        ShouldRefesh = true,
                        ContactPush = false,
                        //CRMPush = true,
                        DirectoryPush = false,
                        UnknownPush = false,
                        CalendarNotifications = true
                        //CallNotifications = user.CallNotifications
                    };
                    wepdbs.UserDevices.Add(userDevice);
                    wepdbs.SaveChanges();
                    // we.SaveChanges();
                }

                //var userPhone = wepdbs.UserPhones.FirstOrDefault(w => w.UserId == existingUser.Id && w.SanitizedNumber == existingUser.SanitizedNumber);
                //if (userPhone != null)
                //    userPhone.IsPhoneValidated = false;
                //we.SaveChanges();
                int languageId = we.Languages.FirstOrDefault(w => w.Locale == validationModel.LanguageId).Id;
                existingUser.LanguageId = languageId;
                wepdbs.SaveChanges();

                //TODO: Orgdirectory
                //var orgDirectoryExist = weo.OrgDirectories.Where(w => w.SanitizedNumber == existingUser.SanitizedNumber && w.IsActive);
                //foreach (var orgDirectory in orgDirectoryExist)
                //{
                //    var provisionExist = weo.Provisions.FirstOrDefault(w => w.OrgDirectoryId == orgDirectory.Id && w.IsActive == true);
                //    if (provisionExist != null) { provisionExist.UserId = existingUser.Id; }
                //    orgDirectory.UserId = existingUser.Id.ToString();
                //}
                //weo.SaveChanges();

                #region Adding provisions for all providers
                var provisionsExist = weodb.Provisions.Where(w => w.UserId == user.Id && w.ProviderTypeId == CommonData.CRMProviderTyepId).ToList();
                if (provisionsExist.Count == 0)
                {
                    Guid orgID = CommonData.WhatElseCustomerOrgId;

                    bool isProvisioned = false;
                    bool isAccepted = true;
                    bool isTrail = false;
                    bool isClaimed = true;
                    var userDomain = user.Email.Split('@');
                    var domainName = userDomain[1].ToLower();
                    // var isfreeDomain = weddb.FreeDomains.FirstOrDefault(w => w.DomainName.ToLower() == domainName.ToLower());
                    var orgExist = weodb.Orgs.Any(w => w.Domains == domainName);
                    if (orgExist == true)
                    {
                        var org = weodb.Orgs.FirstOrDefault(w => w.Domains.ToLower() == domainName.ToLower());
                        isProvisioned = true;
                        isAccepted = true;
                        isTrail = false;
                        isClaimed = false;
                        orgID = org.Id;
                    }
                    else
                    {
                        var country = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
                        var org = new OnePage.Models.Models.OrgModel.Org();
                        org.Id = Guid.NewGuid();
                        org.IsBeta = false;
                        org.AppId = CommonData.GlobalAppId.ToString();
                        org.ModifiedDate = DateTime.UtcNow;
                        org.CreatedDate = DateTime.UtcNow;
                        org.AllowedAccounts = 1;
                        org.Name = domainName.ToLower();
                        org.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
                        org.Countries = country.Name + "|" + country.Prefix;
                        org.Domains = domainName;
                        org.IsActive = true;
                        org.IsRestricted = false;
                        org.IsProvider = false;
                        org.IsPurchaser = false;
                        org.IsAdminGivenFree = false;
                        org.IsInternal = false;
                        weodb.Orgs.Add(org);
                        weodb.SaveChanges();

                        orgID = org.Id;
                        isProvisioned = true;
                        isAccepted = true;
                        isTrail = false;
                        isClaimed = false;

                        //if (isfreeDomain == null)
                        //{
                        //    ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");

                        //    //ApiConfig.Configure("mapit4me", "live_adovCI4ou0Wcdb0BokjBE0Zcg9N2nFJgz");

                        //    EntityResult result = ChargeBee.Models.Customer.Create()
                        //                 .FirstName("Company")
                        //                 .LastName("OAYAW")
                        //                 .Email(user.Email)
                        //                 .Company("Company - " + domainName).Request();
                        //    ChargeBee.Models.Customer customer = result.Customer;
                        //    ChargeBee.Models.Card card = result.Card;
                        //    //user.ChargebeeCustomerId = customer.Id;
                        //    wepdb.SaveChanges();
                        //    //long seconds = DateTimeHelpers.ToEpoch(DateTime.UtcNow);
                        //    //EntityResult result2 = Subscription.CreateForCustomer(customer.Id)
                        //    //                       .PlanId("standard")
                        //    //                       .StartDate(seconds)
                        //    //                       .Request();

                        //    //Subscription subscription = result2.Subscription;
                        //    //Customer customer2 = result2.Customer;
                        //    //ChargeBee.Models.Card card2 = result2.Card;
                        //    //Invoice invoice = result2.Invoice;
                        //    //List<UnbilledCharge> unbilledCharges = result2.UnbilledCharges;
                        //}
                        //else
                        //{
                        //    ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");

                        //    //ApiConfig.Configure("mapit4me", "live_adovCI4ou0Wcdb0BokjBE0Zcg9N2nFJgz");

                        //    EntityResult result = ChargeBee.Models.Customer.Create()
                        //                 .FirstName("Company")
                        //                 .LastName("OAYAW")
                        //                 .Email(user.Email)
                        //                 .Company("Freedomain - " + domainName).Request();
                        //    ChargeBee.Models.Customer customer = result.Customer;
                        //    ChargeBee.Models.Card card = result.Card;
                        //    //user.ChargebeeCustomerId = customer.Id;
                        //    wepdb.SaveChanges();
                        //    //long seconds = DateTimeHelpers.ToEpoch(DateTime.UtcNow);
                        //    //EntityResult result2 = Subscription.CreateForCustomer(customer.Id)
                        //    //                       .PlanId("standard")
                        //    //                       .StartDate(seconds)
                        //    //                       .Request();

                        //    //Subscription subscription = result2.Subscription;
                        //    //Customer customer2 = result2.Customer;
                        //    //ChargeBee.Models.Card card2 = result2.Card;
                        //    //Invoice invoice = result2.Invoice;
                        //    //List<UnbilledCharge> unbilledCharges = result2.UnbilledCharges;
                        //}
                    }

                    var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w => w.OrgId == orgID && w.UserId == user.Id.ToString());
                    if (orgUserExist == null)
                    {
                        var orgUser = new OrgUser();
                        orgUser.Id = Guid.NewGuid();
                        orgUser.IsActive = true;
                        orgUser.CreatedDate = DateTime.UtcNow;
                        orgUser.ActivatedOn = DateTime.UtcNow;
                        orgUser.ModifiedDate = DateTime.UtcNow;
                        orgUser.OrgId = orgID;
                        orgUser.UserId = user.Id;
                        orgUser.IsAdmin = false;
                        weodb.OrgUsers.Add(orgUser);
                        weodb.SaveChanges();

                        var orgDirectory = new OrgDirectory();
                        orgDirectory.Id = Guid.NewGuid();
                        orgDirectory.OrgId = orgID;
                        orgDirectory.CreatedDate = DateTime.UtcNow;
                        orgDirectory.ModifiedDate = DateTime.UtcNow;
                        orgDirectory.IsActive = true;
                        orgDirectory.FirstName = user.FirstName ?? "";
                        orgDirectory.MiddleName = user.MiddleName ?? "";
                        orgDirectory.LastName = user.LastName ?? "";
                        orgDirectory.SanitizedNumber = "";
                        orgDirectory.ProvidedNumber = "";
                        orgDirectory.Email = user.Email ?? "";
                        orgDirectory.Designation = "";
                        orgDirectory.Salutation = "";
                        orgDirectory.UserId = user.Id.ToString();
                        orgDirectory.CountryId = user.CountryId;
                        weodb.OrgDirectories.Add(orgDirectory);
                        weodb.SaveChanges();
                    }
                    else
                    {
                        orgUserExist.ModifiedDate = DateTime.UtcNow;
                        var existing = weodb.OrgUsers.Where(w => w.UserId == user.Id).FirstOrDefault();
                        existing.ModifiedDate = DateTime.UtcNow;
                    }
                    UpdateProvidersFromOrg(user, orgID);
                    weodb.SaveChanges();
                }
                #endregion

                var twitterProvision = weodb.Provisions.Where(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.TwitterProviderId && w.UserId == user.Id && w.EmailAddress == user.Email).FirstOrDefault();
                var tprovision = new Provision();
                if (twitterProvision == null)
                {
                    tprovision.Id = Guid.NewGuid();
                    tprovision.OrgId = CommonData.WhatElseCustomerOrgId;
                    tprovision.AppId = user.AppId;
                    tprovision.UserCustomerId = user.ChargebeeCustomerId;
                    tprovision.ProviderTypeId = CommonData.SocialNetworkProviderTypeId;
                    tprovision.ProviderId = CommonData.TwitterProviderId;
                    tprovision.UserId = user.Id;
                    tprovision.UserProviderId = Guid.Empty;
                    tprovision.CreatedDate = DateTime.UtcNow;
                    tprovision.ModifiedDate = DateTime.UtcNow;
                    tprovision.IsActive = true;
                    tprovision.IsConverted = false;
                    tprovision.IsEnterpriseConverted = false;
                    //tprovision.SanitizedNumber = user.SanitizedNumber;
                    tprovision.IsRedeemed = false;
                    //tprovision.PhoneNumber = user.ProvidedNumber ?? "";
                    //tprovision.Salutation = user.Salutation;
                    tprovision.FirstName = user.FirstName ?? "";
                    tprovision.LastName = user.LastName ?? "";
                    tprovision.MiddleName = user.MiddleName ?? "";
                    tprovision.CountryId = user.CountryId ?? "";
                    tprovision.UserId = user.Id;
                    tprovision.IsFree = false;
                    tprovision.IsProvisioned = true;
                    tprovision.IsPayed = false;
                    tprovision.IsRequested = false;
                    tprovision.IsAccepted = true;
                    tprovision.IsPurchasedByUser = false;
                    tprovision.IsPurchasedOnAndroid = false;
                    tprovision.IsPurchasedOnIos = false;
                    tprovision.EmailAddress = user.Email;
                    tprovision.IsClaimed = false;
                    tprovision.IsTrial = false;
                    weodb.Provisions.Add(tprovision);
                    weodb.SaveChanges();
                }
                else
                    tprovision = twitterProvision;
                //var existingTUserProvider = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.IsActive == true && w.ProviderId == CommonData.TwitterProviderId);
                var x = _userProviderCaching
                    .GetUserProvidersFromRedisOrDbAsyncWithUserId(user.Id, CommonData.TwitterProviderId).GetAwaiter()
                    .GetResult();
                var existingTUserProvider = x != null;
                if (!existingTUserProvider)
                {

                    var newTUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = user.Id,
                        AppId = user.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.TwitterProviderId,
                        IsActive = true,
                        IsAuthenticated = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = tprovision.Id,
                        EmailAddress = user.Email
                    };
                    // wepdb.UserProviders.Add(newTUserProvider1);
                    // wepdb.SaveChanges();
                    _userProviderCaching.AddUserProviderInRedisAndDb(newTUserProvider1).GetAwaiter().GetResult();

                }

                var ppldataProvision = weodb.Provisions.Where(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.PeopleDataProviderId && w.UserId == user.Id && w.EmailAddress == user.Email).FirstOrDefault();

                var provision = new Provision();
                if (ppldataProvision == null)
                {
                    provision.Id = Guid.NewGuid();
                    provision.OrgId = CommonData.WhatElseCustomerOrgId;
                    provision.AppId = user.AppId;
                    provision.UserCustomerId = user.ChargebeeCustomerId;
                    provision.ProviderTypeId = CommonData.CompanyInfoProviderTypeId;
                    provision.ProviderId = CommonData.PeopleDataProviderId;
                    provision.UserId = user.Id;
                    provision.UserProviderId = Guid.Empty;
                    provision.CreatedDate = DateTime.UtcNow;
                    provision.ModifiedDate = DateTime.UtcNow;
                    provision.IsActive = true;
                    provision.IsConverted = false;
                    provision.IsEnterpriseConverted = false;
                    //provision.SanitizedNumber = user.SanitizedNumber;
                    provision.IsRedeemed = false;
                    //provision.PhoneNumber = user.ProvidedNumber ?? "";
                    //provision.Salutation = user.Salutation;
                    provision.FirstName = user.FirstName ?? "";
                    provision.LastName = user.LastName ?? "";
                    provision.MiddleName = user.MiddleName ?? "";
                    provision.CountryId = user.CountryId ?? "";
                    provision.UserId = user.Id;
                    provision.IsFree = false;
                    provision.IsProvisioned = true;
                    provision.IsPayed = false;
                    provision.IsRequested = false;
                    provision.IsAccepted = true;
                    provision.IsPurchasedByUser = false;
                    provision.IsPurchasedOnAndroid = false;
                    provision.IsPurchasedOnIos = false;
                    provision.EmailAddress = user.Email;
                    provision.IsClaimed = false;
                    provision.IsTrial = false;
                    weodb.Provisions.Add(provision);
                    weodb.SaveChanges();
                }
                else
                    provision = ppldataProvision;
                //var existingUserProvider = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.IsActive == true && w.ProviderId == CommonData.PeopleDataProviderId);
                var y = _userProviderCaching
                    .GetUserProvidersFromRedisOrDbAsyncWithUserId(user.Id, CommonData.PeopleDataProviderId).GetAwaiter()
                    .GetResult();
                var existingUserProvider = y != null;
                if (!existingUserProvider)
                {

                    var newUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = user.Id,
                        AppId = user.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.PeopleDataProviderId,
                        IsActive = true,
                        IsAuthenticated = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = provision.Id,
                        EmailAddress = user.Email
                    };
                    // wepdb.UserProviders.Add(newUserProvider1);
                    // wepdb.SaveChanges();
                    _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider1).GetAwaiter().GetResult();

                }

            }
            catch (Exception ex)
            {
                // telemetryTracker.TrackException(ex);
            }
        }
        public async Task CheckForSubscription(Guid userId)
        {
            try
            {
                Guid providerId = Guid.Parse("327915F0-677A-444C-99A8-1B4998A4623C");
                var orgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
                //var redeemed = wepdb.UserProviders.FirstOrDefault(w => w.ProviderId == providerId && w.UserId == userId && w.IsActive == true);
                var redeemed = await _userProviderCaching.GetUserProvidersFromRedisOrDbAsyncWithUserId(userId, providerId);
                Provider provider = weddb.Providers
                     .Include(a => a.ProviderUrls).ThenInclude(w => w.Url).ThenInclude(w => w.Headers)
                     .Include(a => a.ProviderUrls).ThenInclude(w => w.Url).ThenInclude(w => w.Posts)
                     .First(w => w.Id == providerId);
                var identifiersToSend = await providerService.GetIdentifiersForUserProvider(redeemed);

                Url userRightsUrl = provider.ProviderUrls.Where(w => w.Url.ShowOrder == 12400 && w.IsActive.Value).Select(w => w.Url).FirstOrDefault();
                if (userRightsUrl != null)
                {
                    var output = await providerService.MakeRequest<JToken>(userRightsUrl, redeemed, identifiersToSend);
                    List<JToken> outputTokens = output["value"].ToList();
                    if (outputTokens.Count > 0)
                    {
                        foreach (var outputItem in outputTokens.ToList())
                        {
                            try
                            {
                                Guid tasksProdId = Guid.Parse("08A4E0AA-12AB-40B1-9D8A-CDB8C75073E2");
                                Guid PurchaseFromSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF18");
                                Guid PurchasedPlanSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF19");
                                Guid PaymentCompleteSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF11");
                                string userRightId = outputItem.SelectToken("$.id").Value<string>();
                                string catalogId = outputItem.SelectToken("$.catalogId").Value<string>();
                                string serviceIdentifier = outputItem.SelectToken("$.serviceIdentifier").Value<string>();
                                string stateValue = outputItem.SelectToken("$.state").Value<string>();
                                if (stateValue == "active")
                                {
                                    bool shouldContinue = true;
                                    var checkForPaymentComplete = wepdb.UserSettings.FirstOrDefault(w => w.UserId == userId && w.SettingId == PaymentCompleteSettingId && w.IsActive == true);
                                    if (checkForPaymentComplete != null)
                                    {
                                        var paymentCompleteSetting = wepdb.UserSettings.FirstOrDefault(w => w.UserId == userId && w.SettingId == PaymentCompleteSettingId && w.IsActive == true);
                                        if (paymentCompleteSetting.Value == "1")
                                        {
                                            shouldContinue = false;
                                        }
                                        else
                                        {
                                            shouldContinue = true;
                                        }
                                    }
                                    if (shouldContinue == false)
                                        return;
                                    await SetUserSettings(userId, PurchaseFromSettingId, "Microsoft");
                                    await SetUserSettings(userId, PaymentCompleteSettingId, "1");
                                    if (serviceIdentifier.Contains("professional"))
                                    {
                                        await SetUserSettings(userId, PurchasedPlanSettingId, "Professional");
                                        var userSettings1 = wepdb.UserSettings.Where(w => w.UserId == userId && w.IsActive == true).ToList();
                                        if (userSettings1.Count > 0)
                                        {
                                            var vanishingCredits1 = userSettings1.First(w => w.SettingId == Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802") && w.UserId == userId && w.IsActive == true);
                                            var vCreditValues1 = int.Parse(vanishingCredits1.Value);
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), (vCreditValues1 + 100).ToString());
                                        }
                                        CreditActivity creditActivity1 = new CreditActivity();
                                        creditActivity1.Id = Guid.NewGuid();
                                        creditActivity1.UserId = userId;
                                        creditActivity1.Redeemdate = DateTime.UtcNow;
                                        creditActivity1.IsActive = true;
                                        creditActivity1.CreatedDate = DateTime.UtcNow;
                                        creditActivity1.ModifiedDate = DateTime.UtcNow;
                                        creditActivity1.Credit = 100;
                                        creditActivity1.ReceiptId = DateTime.UtcNow.ToString();
                                        creditActivity1.TransactionDateUtc = DateTime.UtcNow;
                                        creditActivity1.ProductId = wepdb.InApps.First(w => w.Id == tasksProdId).ProductId;
                                        creditActivity1.AutoRenewing = false;
                                        wepdb.CreditActivities.Add(creditActivity1);
                                        wepdb.SaveChanges();
                                    }
                                    else if (serviceIdentifier.Contains("enterprise"))
                                    {
                                        await SetUserSettings(userId, PurchasedPlanSettingId, "Enterprise");
                                        var userSettings1 = wepdb.UserSettings.Where(w => w.UserId == userId && w.IsActive == true).ToList();
                                        if (userSettings1.Count > 0)
                                        {
                                            var vanishingCredits1 = userSettings1.First(w => w.SettingId == Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802") && w.UserId == userId && w.IsActive == true);
                                            var vCreditValues1 = int.Parse(vanishingCredits1.Value);
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), (vCreditValues1 + 200).ToString());
                                        }
                                        CreditActivity creditActivity1 = new CreditActivity();
                                        creditActivity1.Id = Guid.NewGuid();
                                        creditActivity1.UserId = userId;
                                        creditActivity1.Redeemdate = DateTime.UtcNow;
                                        creditActivity1.IsActive = true;
                                        creditActivity1.CreatedDate = DateTime.UtcNow;
                                        creditActivity1.ModifiedDate = DateTime.UtcNow;
                                        creditActivity1.Credit = 200;
                                        creditActivity1.ReceiptId = DateTime.UtcNow.ToString();
                                        creditActivity1.TransactionDateUtc = DateTime.UtcNow;
                                        creditActivity1.ProductId = wepdb.InApps.First(w => w.Id == tasksProdId).ProductId;
                                        creditActivity1.AutoRenewing = false;
                                        wepdb.CreditActivities.Add(creditActivity1);
                                        wepdb.SaveChanges();
                                    }
                                    else if (serviceIdentifier.Contains("salesperson"))
                                    {
                                        await SetUserSettings(userId, PurchasedPlanSettingId, "Salesperson");
                                        var userSettings1 = wepdb.UserSettings.Where(w => w.UserId == userId && w.IsActive == true).ToList();
                                        if (userSettings1.Count > 0)
                                        {
                                            var vanishingCredits1 = userSettings1.First(w => w.SettingId == Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802") && w.UserId == userId && w.IsActive == true);
                                            var vCreditValues1 = int.Parse(vanishingCredits1.Value);
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), (vCreditValues1 + 200).ToString());
                                        }
                                        CreditActivity creditActivity1 = new CreditActivity();
                                        creditActivity1.Id = Guid.NewGuid();
                                        creditActivity1.UserId = userId;
                                        creditActivity1.Redeemdate = DateTime.UtcNow;
                                        creditActivity1.IsActive = true;
                                        creditActivity1.CreatedDate = DateTime.UtcNow;
                                        creditActivity1.ModifiedDate = DateTime.UtcNow;
                                        creditActivity1.Credit = 200;
                                        creditActivity1.ReceiptId = DateTime.UtcNow.ToString();
                                        creditActivity1.TransactionDateUtc = DateTime.UtcNow;
                                        creditActivity1.ProductId = wepdb.InApps.First(w => w.Id == tasksProdId).ProductId;
                                        creditActivity1.AutoRenewing = false;
                                        wepdb.CreditActivities.Add(creditActivity1);
                                        wepdb.SaveChanges();
                                    }
                                }
                                else if (stateValue == "inactive")
                                {
                                    Guid TrialCompleteSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF10");
                                    await SetUserSettings(userId, PaymentCompleteSettingId, "0");
                                    await SetUserSettings(userId, PurchasedPlanSettingId, "");
                                    var isTrialComplete = wepdb.UserSettings.FirstOrDefault(w => w.SettingId == TrialCompleteSettingId && w.UserId == userId && w.IsActive == true);
                                    if (isTrialComplete != null)
                                    {
                                        if (isTrialComplete.Value == "1")
                                        {
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), "0");
                                        }
                                        else if (isTrialComplete.Value == "0")
                                        {
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), "10");
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                telemetryTracker.TrackException(ex, userId);

                            }
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);

            }
        }
        async Task SetUserSettings(Guid userId, Guid settingId, string settingValue)
        {
            try
            {
                bool IsPaidUser = weodb.OrgUsers.Any(w => w.UserId == userId && w.Org.IsPaid == true);
                var setting = wepdb.Settings.First(w => w.Id == settingId);
                if (!setting.IsAdmin)
                {
                    if (wepdb.UserSettings.Any(w => w.UserId == userId && w.SettingId == settingId))
                    {
                        var existing = wepdb.UserSettings.First(w => w.UserId == userId && w.SettingId == settingId);
                        existing.Value = settingValue;
                        existing.IsActive = true;
                        existing.ModifiedDate = DateTime.UtcNow;
                    }
                    else
                    {
                        var userSetting = new UserSetting
                        {
                            Id = Guid.NewGuid(),
                            IsActive = true,
                            SettingId = settingId,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            UserId = userId,
                            DataType = setting.DataType,
                            Value = settingValue
                        };
                        wepdb.UserSettings.Add(userSetting);
                    }
                    wepdb.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);

            }
        }
        public void UpdateOtherTablesUsingEmailForVerify(UserOtpVerifyPayload validationModel, string email, User user, Models.Models.TokenModel.Token token)
        {
            try
            {
                // DataContext we = new DataContext();
                var contextBuilder = new DbContextOptionsBuilder<DataContext>();
                contextBuilder.UseSqlServer(_configuration["DBConnections:WEDataConnectionString"]);

                var contextBuilderP = new DbContextOptionsBuilder<PeopleContext>();
                contextBuilderP.UseSqlServer(_configuration["DBConnections:WEPeopleConnectionString"]);

                var contextBuilderO = new DbContextOptionsBuilder<OrgContext>();
                contextBuilderO.UseSqlServer(_configuration["DBConnections:WEOrgConnectionString"]);

                //   DataContext we = new DataContext(contextBuilder.Options);
                //wesdb = new WESyncEntities();
                OrgContext weo = new OrgContext(contextBuilderO.Options);
                PeopleContext wepdbs = new PeopleContext(contextBuilderP.Options);
                var existingUser = wepdbs.Users.FirstOrDefault(w => w.Id == user.Id);

                bool isPrimary = (validationModel.deviceTypeId == CommonData.IOSDeviceTypeId) ? false : true;

                // to set user device and token to fasle if user logs in same device without uninstalling

                if (validationModel.DeviceData != "")
                {
                    var userDevices = wepdbs.UserDevices.Where(w => w.UserId == existingUser.Id && w.IsActive == true && w.DeviceData == validationModel.DeviceData).ToList();
                    if (userDevices.Count() > 0)
                    {
                        foreach (var userDeviceItem in userDevices)
                        {
                            userDeviceItem.IsActive = false;
                            wepdbs.SaveChanges();
                            var activeToken = wetdb.Tokens.FirstOrDefault(w => w.UserDeviceId == userDeviceItem.Id && w.IsActive == true);
                            if (activeToken != null)
                            {
                                activeToken.IsActive = false;
                                wetdb.SaveChanges();
                            }
                        }
                    }
                }
                if (!wepdbs.UserDevices.Any(x => x.Id == validationModel.deviceId))
                {
                    UserDevice userDevice = new UserDevice()
                    {
                        Id = validationModel.deviceId,
                        DeviceTypeId = validationModel.deviceTypeId,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        //CountryId = countryId,
                        UserId = existingUser.Id,
                        IsPhoneValidated = false,
                        //SanitizedNumber = santizedPhoneNumber,
                        IsPrimary = isPrimary,
                        IsPersonal = true,
                        //PhoneNumber = providedNumber,
                        //PType = Int32.Parse(numberType),
                        DeviceData = validationModel.DeviceData ?? "",
                        ShouldRefesh = true,
                        ContactPush = false,
                        //CRMPush = true,
                        DirectoryPush = false,
                        UnknownPush = false,
                        CalendarNotifications = true
                        //CallNotifications = user.CallNotifications
                    };
                    wepdbs.UserDevices.Add(userDevice);
                    wepdbs.SaveChanges();
                    //  we.SaveChanges();
                }

                //var userPhone = wepdbs.UserPhones.FirstOrDefault(w => w.UserId == existingUser.Id && w.SanitizedNumber == existingUser.SanitizedNumber);
                //if (userPhone != null)
                //    userPhone.IsPhoneValidated = false;
                //we.SaveChanges();
                //int languageId = we.Languages.FirstOrDefault(w => w.Locale == validationModel.languageId).Id;
                // existingUser.LanguageId = languageId;
                // wepdbs.SaveChanges();

                #region Adding provisions for all providers
                var provisionsExist = weodb.Provisions.Where(w => w.UserId == user.Id && w.ProviderTypeId == CommonData.CRMProviderTyepId).ToList();
                if (provisionsExist.Count == 0)
                {
                    Guid orgID = CommonData.WhatElseCustomerOrgId;

                    bool isProvisioned = false;
                    bool isAccepted = true;
                    bool isTrail = false;
                    bool isClaimed = true;
                    var userDomain = user.Email.Split('@');
                    var domainName = userDomain[1].ToLower();
                    // var isfreeDomain = weddb.FreeDomains.FirstOrDefault(w => w.DomainName.ToLower() == domainName.ToLower());
                    var orgExist = weodb.Orgs.Any(w => w.Domains == domainName);
                    if (orgExist == true)
                    {
                        var org = weodb.Orgs.FirstOrDefault(w => w.Domains.ToLower() == domainName.ToLower());
                        isProvisioned = true;
                        isAccepted = true;
                        isTrail = false;
                        isClaimed = false;
                        orgID = org.Id;
                    }
                    else
                    {
                        var country = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
                        var org = new OnePage.Models.Models.OrgModel.Org();
                        org.Id = Guid.NewGuid();
                        org.IsBeta = false;
                        org.AppId = CommonData.GlobalAppId.ToString();
                        org.ModifiedDate = DateTime.UtcNow;
                        org.CreatedDate = DateTime.UtcNow;
                        org.AllowedAccounts = 1;
                        org.Name = domainName.ToLower();
                        org.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
                        org.Countries = country.Name + "|" + country.Prefix;
                        org.Domains = domainName;
                        org.IsActive = true;
                        org.IsRestricted = false;
                        org.IsProvider = false;
                        org.IsPurchaser = false;
                        org.IsAdminGivenFree = false;
                        org.IsInternal = false;
                        weodb.Orgs.Add(org);
                        weodb.SaveChanges();

                        orgID = org.Id;
                        isProvisioned = true;
                        isAccepted = true;
                        isTrail = false;
                        isClaimed = false;

                    }

                    var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w => w.OrgId == orgID && w.UserId == user.Id.ToString());
                    if (orgUserExist == null)
                    {
                        var orgUser = new OrgUser();
                        orgUser.Id = Guid.NewGuid();
                        orgUser.IsActive = true;
                        orgUser.CreatedDate = DateTime.UtcNow;
                        orgUser.ActivatedOn = DateTime.UtcNow;
                        orgUser.ModifiedDate = DateTime.UtcNow;
                        orgUser.OrgId = orgID;
                        orgUser.UserId = user.Id;
                        orgUser.IsAdmin = false;
                        weodb.OrgUsers.Add(orgUser);
                        weodb.SaveChanges();

                        var orgDirectory = new OrgDirectory();
                        orgDirectory.Id = Guid.NewGuid();
                        orgDirectory.OrgId = orgID;
                        orgDirectory.CreatedDate = DateTime.UtcNow;
                        orgDirectory.ModifiedDate = DateTime.UtcNow;
                        orgDirectory.IsActive = true;
                        orgDirectory.FirstName = user.FirstName ?? "";
                        orgDirectory.MiddleName = user.MiddleName ?? "";
                        orgDirectory.LastName = user.LastName ?? "";
                        orgDirectory.SanitizedNumber = "";
                        orgDirectory.ProvidedNumber = "";
                        orgDirectory.Email = user.Email ?? "";
                        orgDirectory.Designation = "";
                        orgDirectory.Salutation = "";
                        orgDirectory.UserId = user.Id.ToString();
                        orgDirectory.CountryId = user.CountryId;
                        weodb.OrgDirectories.Add(orgDirectory);
                        weodb.SaveChanges();
                    }
                    else
                    {
                        orgUserExist.ModifiedDate = DateTime.UtcNow;
                        var existing = weodb.OrgUsers.Where(w => w.UserId == user.Id).FirstOrDefault();
                        existing.ModifiedDate = DateTime.UtcNow;
                    }
                    UpdateProvidersFromOrg(user, orgID);
                    weodb.SaveChanges();
                }
                #endregion

                var twitterProvision = weodb.Provisions.Where(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.TwitterProviderId && w.UserId == user.Id && w.EmailAddress == user.Email).FirstOrDefault();
                var tprovision = new Provision();
                if (twitterProvision == null)
                {
                    tprovision.Id = Guid.NewGuid();
                    tprovision.OrgId = CommonData.WhatElseCustomerOrgId;
                    tprovision.AppId = user.AppId;
                    tprovision.UserCustomerId = user.ChargebeeCustomerId;
                    tprovision.ProviderTypeId = CommonData.SocialNetworkProviderTypeId;
                    tprovision.ProviderId = CommonData.TwitterProviderId;
                    tprovision.UserId = user.Id;
                    tprovision.UserProviderId = Guid.Empty;
                    tprovision.CreatedDate = DateTime.UtcNow;
                    tprovision.ModifiedDate = DateTime.UtcNow;
                    tprovision.IsActive = true;
                    tprovision.IsConverted = false;
                    tprovision.IsEnterpriseConverted = false;
                    //tprovision.SanitizedNumber = user.SanitizedNumber;
                    tprovision.IsRedeemed = false;
                    //tprovision.PhoneNumber = user.ProvidedNumber ?? "";
                    //tprovision.Salutation = user.Salutation;
                    tprovision.FirstName = user.FirstName ?? "";
                    tprovision.LastName = user.LastName ?? "";
                    tprovision.MiddleName = user.MiddleName ?? "";
                    tprovision.CountryId = user.CountryId ?? "";
                    tprovision.UserId = user.Id;
                    tprovision.IsFree = false;
                    tprovision.IsProvisioned = true;
                    tprovision.IsPayed = false;
                    tprovision.IsRequested = false;
                    tprovision.IsAccepted = true;
                    tprovision.IsPurchasedByUser = false;
                    tprovision.IsPurchasedOnAndroid = false;
                    tprovision.IsPurchasedOnIos = false;
                    tprovision.EmailAddress = user.Email;
                    tprovision.IsClaimed = false;
                    tprovision.IsTrial = false;
                    weodb.Provisions.Add(tprovision);
                    weodb.SaveChanges();
                }
                else
                    tprovision = twitterProvision;
                // var existingTUserProvider = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.IsActive == true && w.ProviderId == CommonData.TwitterProviderId);
                var x = _userProviderCaching.GetUserProvidersFromRedisOrDbAsyncWithUserId(user.Id,
                    TwitterProviderId).GetAwaiter().GetResult();
                var existingTUserProvider = x != null;
                if (!existingTUserProvider)
                {

                    var newTUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = user.Id,
                        AppId = user.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.TwitterProviderId,
                        IsActive = true,
                        IsAuthenticated = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = tprovision.Id,
                        EmailAddress = user.Email
                    };
                    // wepdb.UserProviders.Add(newTUserProvider1);
                    // wepdb.SaveChanges();
                    _userProviderCaching.AddUserProviderInRedisAndDb(newTUserProvider1).GetAwaiter().GetResult();

                }

                var ppldataProvision = weodb.Provisions.Where(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.PeopleDataProviderId && w.UserId == user.Id && w.EmailAddress == user.Email).FirstOrDefault();

                var provision = new Provision();
                if (ppldataProvision == null)
                {
                    provision.Id = Guid.NewGuid();
                    provision.OrgId = CommonData.WhatElseCustomerOrgId;
                    provision.AppId = user.AppId;
                    provision.UserCustomerId = user.ChargebeeCustomerId;
                    provision.ProviderTypeId = CommonData.CompanyInfoProviderTypeId;
                    provision.ProviderId = CommonData.PeopleDataProviderId;
                    provision.UserId = user.Id;
                    provision.UserProviderId = Guid.Empty;
                    provision.CreatedDate = DateTime.UtcNow;
                    provision.ModifiedDate = DateTime.UtcNow;
                    provision.IsActive = true;
                    provision.IsConverted = false;
                    provision.IsEnterpriseConverted = false;
                    //provision.SanitizedNumber = user.SanitizedNumber;
                    provision.IsRedeemed = false;
                    //provision.PhoneNumber = user.ProvidedNumber ?? "";
                    //provision.Salutation = user.Salutation;
                    provision.FirstName = user.FirstName ?? "";
                    provision.LastName = user.LastName ?? "";
                    provision.MiddleName = user.MiddleName ?? "";
                    provision.CountryId = user.CountryId ?? "";
                    provision.UserId = user.Id;
                    provision.IsFree = false;
                    provision.IsProvisioned = true;
                    provision.IsPayed = false;
                    provision.IsRequested = false;
                    provision.IsAccepted = true;
                    provision.IsPurchasedByUser = false;
                    provision.IsPurchasedOnAndroid = false;
                    provision.IsPurchasedOnIos = false;
                    provision.EmailAddress = user.Email;
                    provision.IsClaimed = false;
                    provision.IsTrial = false;
                    weodb.Provisions.Add(provision);
                    weodb.SaveChanges();
                }
                else
                    provision = ppldataProvision;
                //var existingUserProvider = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.IsActive == true && w.ProviderId == CommonData.PeopleDataProviderId);
                var y = _userProviderCaching.GetUserProvidersFromRedisOrDbAsyncWithUserId(user.Id,
                    PeopleDataProviderId).GetAwaiter().GetResult();
                var existingUserProvider = y != null;
                if (!existingUserProvider)
                {

                    var newUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = user.Id,
                        AppId = user.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.PeopleDataProviderId,
                        IsActive = true,
                        IsAuthenticated = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = provision.Id,
                        EmailAddress = user.Email
                    };
                    // wepdb.UserProviders.Add(newUserProvider1);
                    // wepdb.SaveChanges();
                    _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider1).GetAwaiter().GetResult();

                }

            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, Guid.Empty);

            }
        }

        public class SSOTokenModel
        {
            public string Token { get; set; }
        }
        private string FindAndReplaceIndentifiers(string finalURL)
        {
            foreach (var item in identifierList)
            {
                if (item.Value != "" && item.Value != null)
                {
                    finalURL = finalURL.Replace("{" + item.Name + "}", item.Value);
                }
            }
            foreach (var orgIdentifier in orgProviderIdentifierList)
            {
                var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == orgIdentifier.IdentifierId).Name;
                finalURL = finalURL.Replace("{" + identifierName + "}", orgIdentifier.Value);
            }
            finalURL = finalURL.Replace("\r\n", "");
            _redisCaching.SetData(finalURL, finalURL, DateTimeOffset.Now.AddDays(7));
            return finalURL;
        }

        public async Task<bool> PostToken(string token, Url providerUrl)
        {
            try
            {
                var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
                foreach (var outputIdentifier in outputIndentifiers)
                {
                    var keyData = outputIdentifier.Split('|');
                    // var selectedToken = jtoken.SelectToken(keyData[1]);
                    //if (selectedToken != null)
                    //{
                    //  string tokenValue = selectedToken.Value<string>();
                    foreach (var identifier in identifierList)
                    {
                        if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
                        {
                            identifier.Value = token;
                            TelegramService.SendMessageToTestBot(token.ToString());
                        }
                    }
                    //}
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, Guid.Empty);
                TelegramService.SendMessageToTestBot(ex.StackTrace.ToString());
                TelegramService.SendMessageToTestBot(ex.ToString());
                return false;
            }
            return true;
        }
        void UpdateProvidersFromOrg(User user, Guid orgID)
        {
            try
            {
                var activeProviders = weddb.Providers.Where(w => w.IsActive == true && w.ProviderTypeId != CommonData.CompanyInfoProviderTypeId).ToList();

                foreach (var activeProvider in activeProviders)
                {
                    try
                    {
                        var provider = weddb.Providers.FirstOrDefault(w => w.Id == activeProvider.Id && w.IsActive == true);

                        var orgProviderExist = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == orgID && w.ProviderId == provider.Id && w.IsActive == true);
                        if (orgProviderExist == null)
                        {
                            var orgProvider = new OrgProvider();
                            orgProvider.Id = Guid.NewGuid();
                            orgProvider.CreatedDate = DateTime.UtcNow;
                            orgProvider.ModifiedDate = DateTime.UtcNow;
                            orgProvider.IsActive = true;
                            orgProvider.OrgId = orgID;
                            orgProvider.ProviderId = provider.Id;
                            orgProvider.CanSaveForOffline = true;
                            orgProvider.ForceCallLog = true;
                            weodb.OrgProviders.Add(orgProvider);

                            var weOrgProvider = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == provider.Id && w.IsActive == true);
                            if (weOrgProvider != null)
                            {
                                var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();

                                foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
                                {
                                    var orgIdentifier = new OrgIdentifier();
                                    orgIdentifier.Id = Guid.NewGuid();
                                    orgIdentifier.OrgProviderId = orgProvider.Id;
                                    orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
                                    orgIdentifier.IsActive = true;
                                    orgIdentifier.CreatedDate = DateTime.UtcNow;
                                    orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                    orgIdentifier.ShowSequence = 1;
                                    orgIdentifier.Value = weOrgProviderIdentifier.Value;
                                    weodb.OrgIdentifiers.Add(orgIdentifier);
                                    weodb.SaveChanges();
                                }
                            }
                        }
                        var existingProvision = weodb.Provisions.Where(w => w.OrgId == orgID && w.ProviderId == provider.Id && w.UserId == user.Id && w.EmailAddress == user.Email).FirstOrDefault();
                        if (existingProvision == null)
                        {
                            //TODO: comment for on the fly provisioning
                            #region Provider provision
                            var provision = new Provision();
                            provision.Id = Guid.NewGuid();
                            provision.OrgId = orgID; //CommonData.WhatElseCustomerOrgId;
                            provision.AppId = user.AppId;
                            provision.UserCustomerId = user.ChargebeeCustomerId;
                            provision.ProviderTypeId = provider.ProviderTypeId;
                            provision.ProviderId = provider.Id;
                            provision.UserId = user.Id;
                            provision.UserProviderId = Guid.Empty;
                            provision.CreatedDate = DateTime.UtcNow;
                            provision.ModifiedDate = DateTime.UtcNow;
                            provision.IsActive = true;
                            provision.IsConverted = false;
                            provision.IsEnterpriseConverted = false;
                            //provision.SanitizedNumber = user.SanitizedNumber;
                            provision.IsRedeemed = false;
                            //provision.PhoneNumber = user.ProvidedNumber ?? "";
                            //provision.Salutation = user.Salutation;
                            provision.FirstName = user.FirstName ?? "";
                            provision.LastName = user.LastName ?? "";
                            provision.MiddleName = user.MiddleName ?? "";
                            provision.CountryId = user.CountryId ?? "";
                            provision.UserId = user.Id;
                            provision.IsFree = false;
                            provision.IsProvisioned = true;
                            provision.IsPayed = false;
                            provision.IsRequested = false;
                            provision.IsAccepted = true;
                            provision.IsPurchasedByUser = false;
                            provision.IsPurchasedOnAndroid = false;
                            provision.IsPurchasedOnIos = false;
                            provision.EmailAddress = user.Email;
                            provision.IsClaimed = false;
                            provision.IsTrial = false;
                            weodb.Provisions.Add(provision);
                            #endregion
                        }
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex, Guid.Empty);
                        Console.WriteLine(ex.ToString());
                    }
                }
                weodb.SaveChanges();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, Guid.Empty);
                Console.WriteLine(ex.ToString());
            }
        }
        void SetUserSettings(Guid userId)
        {
            try
            {
                bool IsPaidUser = weodb.OrgUsers.Any(w => w.UserId == userId && w.Org.IsPaid == true);
                Guid firstSetting = Guid.Parse("1d9bc314-786c-40e4-9fe4-769860c58f01");
                // User without any settings
                var settingList = wepdb.Settings.Where(w => w.IsActive == true).ToList();
                var exisitngSettings = wepdb.UserSettings.Where(x => x.UserId == userId && x.SettingId == firstSetting).FirstOrDefault();
                if (exisitngSettings == null)
                {
                    foreach (var item in settingList.ToList())
                    {
                        try
                        {
                            var vals = item.DefaultValue.Split('|');
                            var userSetting = new UserSetting
                            {
                                Id = Guid.NewGuid(),
                                IsActive = true,
                                SettingId = item.Id,
                                CreatedDate = DateTime.UtcNow,
                                ModifiedDate = DateTime.UtcNow,
                                UserId = userId,
                                DataType = item.DataType,
                                Value = IsPaidUser ? vals[1] : vals[0]
                            };
                            wepdb.UserSettings.Add(userSetting);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.ToString());
                        }
                    }
                    wepdb.SaveChanges();
                }
                else // User missing some settings
                {
                    var userSettings = wepdb.UserSettings.Where(w => w.UserId == userId).ToList();
                    foreach (var item in settingList.ToList())
                    {
                        try
                        {
                            var vals = item.DefaultValue.Split('|');
                            if (!userSettings.Any(w => w.UserId == userId && w.SettingId == item.Id))
                            {
                                try
                                {
                                    var userSetting = new UserSetting
                                    {
                                        Id = Guid.NewGuid(),
                                        IsActive = true,
                                        SettingId = item.Id,
                                        CreatedDate = DateTime.UtcNow,
                                        ModifiedDate = DateTime.UtcNow,
                                        UserId = userId,
                                        DataType = item.DataType,
                                        Value = IsPaidUser ? vals[1] : vals[0]
                                    };
                                    wepdb.UserSettings.Add(userSetting);
                                }
                                catch (Exception ex)
                                {

                                }
                            }
                            //if (item.IsAdmin)
                            //{
                            //    if (!(wepdb.UserSettings.Any(w => w.UserId == userId && w.SettingId == item.Id)))
                            //    {
                            //        var userSetting = new UserSetting
                            //        {
                            //            Id = Guid.NewGuid(),
                            //            IsActive = true,
                            //            SettingId = item.Id,
                            //            CreatedDate = DateTime.UtcNow,
                            //            ModifiedDate = DateTime.UtcNow,
                            //            UserId = userId,
                            //            DataType = item.DataType,
                            //            Value = IsPaidUser ? vals[1] : vals[0]
                            //        };
                            //        wepdb.UserSettings.Add(userSetting);
                            //    }
                            //}
                        }
                        catch (Exception ex)
                        {
                            telemetryTracker.TrackException(ex, userId);
                            Console.WriteLine(ex.ToString());
                        }
                    }
                    wepdb.SaveChanges();

                }

            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                Console.WriteLine(ex.ToString());
            }
        }
        void SetOrgLevelSettings(Guid userId)
        {
            try
            {
                var orgId = weodb.OrgUsers.First(w => w.UserId == userId && w.OrgId != Common.CommonData.WhatElseCustomerOrgId && w.IsActive == true).OrgId;
                if (orgId != Guid.Empty)
                {
                    Guid TeamsProviderId = Guid.Parse("5CC37A0C-96DD-4E30-93C7-A0A991156640");
                    var orgProviderExists = weodb.OrgProviders.Any(w => w.OrgId == orgId && w.ProviderId == TeamsProviderId && w.IsActive == true);
                    if (orgProviderExists)
                    {
                        var orgProvider = weodb.OrgProviders.First(w => w.OrgId == orgId && w.ProviderId == TeamsProviderId && w.IsActive == true);
                        Guid TeamsConsentIId = Guid.Parse("3A225307-704A-4E89-AC95-07169F88A008");
                        var teamsConsentIdentifier = weodb.OrgIdentifiers.First(w => w.OrgProviderId == orgProvider.Id && w.IsActive == true && w.IdentifierId == TeamsConsentIId);

                        var teamsConsentSettingId = Guid.Parse("2E2A7614-0BBC-40F5-A643-6DDBE3610000");
                        var tcSetting = wepdb.UserSettings.First(w => w.UserId == userId && w.SettingId == teamsConsentSettingId && w.IsActive == true);
                        tcSetting.Value = teamsConsentIdentifier.Value;
                        tcSetting.IsUserUpdated = true;
                        wepdb.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot2(ex.ToString());
                TelegramService.SendMessageToTestBot2(ex.StackTrace);
                TelegramService.SendMessageToTestBot2("internal exception : " + ex.InnerException.Message);
                telemetryTracker.TrackException(ex, userId);

            }
        }
        public async Task<bool> GetClient(string finalURL, Url providerUrl, string dataToReplace)
        {
            try
            {
                var urlPart1 = finalURL;
                HttpClient client = new HttpClient();
                HttpContent content = new StringContent(String.Empty);
                content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
                foreach (var item in headersList)
                {
                    if (item.Value.Contains('{'))
                    {
                        item.Value = FindAndReplaceIndentifiers(item.Value);
                    }
                    if (item.Name == "Content-Type")
                        content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
                    else
                        client.DefaultRequestHeaders.Add(item.Name, item.Value);
                }
                TelegramService.SendMessageToTestBot("Get Method calll!");
                using (HttpResponseMessage response1 = await client.GetAsync(urlPart1))
                {
                    TelegramService.SendMessageToTestBot(response1.StatusCode.ToString());
                    TelegramService.SendMessageToTestBot(response1.RequestMessage.ToString());
                    if (response1.IsSuccessStatusCode)
                    {
                        try
                        {
                            var json = await response1.Content.ReadAsStringAsync();
                            CommonData.sbnew.AppendLine("me api response: " + json);
                            JToken jtoken = JToken.Parse(json);
                            var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
                            foreach (var outputIdentifier in outputIndentifiers)
                            {
                                JToken selectedToken;
                                List<JToken> tokenList = new List<JToken>();
                                var keyData = outputIdentifier.Split('|');
                                if (keyData[1].Contains(".."))
                                {
                                    var list = jtoken.SelectTokens(keyData[1]).Values<string>().ToList();
                                    string tokenValues = "";
                                    foreach (var item in list.ToList())
                                    {
                                        string tokenValue = item;
                                        tokenValues += tokenValue + ",";
                                    }
                                    if (tokenValues.Length > 0)
                                        tokenValues = tokenValues.Substring(0, tokenValues.Length - 1);
                                    foreach (var identifier in identifierList)
                                    {
                                        if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
                                        {
                                            identifier.Value = tokenValues;
                                        }
                                    }
                                }
                                else
                                {
                                    selectedToken = jtoken.SelectToken(keyData[1]);
                                    if (selectedToken != null)
                                    {
                                        if (selectedToken.Type == JTokenType.Array)
                                        {
                                            var tokens = selectedToken.Values();
                                            string tokenValues = "";
                                            foreach (var item in tokens)
                                            {
                                                string tokenValue = item.Value<string>();
                                                tokenValues += tokenValue + ",";
                                            }
                                            if (tokenValues.Length > 0)
                                                tokenValues = tokenValues.Substring(0, tokenValues.Length - 1);
                                            foreach (var identifier in identifierList)
                                            {
                                                if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
                                                {
                                                    identifier.Value = tokenValues;
                                                }
                                            }
                                        }
                                        else
                                        {
                                            string tokenValue = selectedToken.Value<string>();
                                            foreach (var identifier in identifierList)
                                            {
                                                if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
                                                {
                                                    identifier.Value = tokenValue;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            telemetryTracker.TrackException(ex, Guid.Empty);

                        }
                    }
                    else
                    {
                        //ErrorMailModel errorMailModel = new ErrorMailModel();
                        //errorMailModel.Url = "server" + finalURL;
                        //errorMailModel.Count = 0;
                        //errorMailModel.CreatedDate = DateTime.UtcNow;
                        //errorMailModel.ErrorCode = response1.StatusCode.ToString();
                        //if (response1.RequestMessage.Content == null)
                        //{
                        //    errorMailModel.ErrorMessage = errorMailModel.ErrorCode;
                        //}
                        //else
                        //{
                        //    errorMailModel.ErrorMessage = response1.RequestMessage.Content.ToString();
                        //}
                        //errorMailModel.UserProviderId = Guid.Empty;
                        ////   errorMailModel.ProvisionId = Guid.Parse(appProvision);
                        //errorMailModel.Payload = dataToReplace ?? "";
                        //errorMailModel.Email = "Didn't find emailid";
                        //var mail = EmailServices.SendGridErrorMail(errorMailModel);
                    }
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, Guid.Empty);
                TelegramService.SendMessageToTestBot(ex.ToString());
                TelegramService.SendMessageToTestBot(ex.StackTrace.ToString());
                return false;
            }
            return true;
        }
        public class PaymentStatus
        {
            public string Status { get; set; }
        }
        [HttpPost]
        [Route("C605CB1C")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPaymentStatusBySesionId(string sesionId)
        {
            try
            {
                var ApiKey = _configuration["stripeKey"];
                // var ApiKey = CommonData.StripeKey;
                var finalurl = CommonData.StripeAPI.Replace("{sessionId}", sesionId);
                var client = new RestSharp.RestClient(finalurl);
                var request = new RestRequest(Method.GET);
                request.AddHeader("Authorization", "Basic " + Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(ApiKey)));
                IRestResponse response = await client.ExecuteAsync(request);
                var contentJson = JsonConvert.SerializeObject(response.Content);
                var paymentToken = JsonConvert.DeserializeObject<JToken>(response.Content);
                PaymentStatus paymentStatus = new PaymentStatus();
                paymentStatus.Status = paymentToken.SelectToken("$.payment_status").Value<string>();
                return Ok(paymentStatus);


            }
            catch (Exception ex)
            {
                PaymentStatus paymentStatus = new PaymentStatus();
                paymentStatus.Status = "SessionId is not found";
                return BadRequest(paymentStatus);

            }
        }
        [HttpPost]
        [Route("0CB59C4F")]

        public async Task<IActionResult> SendOTPTOUserMail([FromQuery] string userEmail)
        {
            var userId = _authService.GetUser(Request);
            try
            {
                if (userId == Guid.Empty)
                {
                    return Unauthorized();
                }

                var userInfo = await wepdb.Users.FirstOrDefaultAsync(w => w.Id == userId);

                var userLanguage = await wepdb.UserSettings.Where(x => x.UserId == userInfo.Id && x.SettingId == CommonData.SelectedLanguageSettingId).Select(x => x.Value).FirstOrDefaultAsync();


                var code = GenerateOtp();
                userInfo.Otp = code;
                await wepdb.SaveChangesAsync();
                //  _redisCaching.SetDataSliding<string>("userInfo_otp" + userId, code, TimeSpan.FromMinutes(10));
                //userLanguage = "es";
                if (userLanguage.Contains("en") || string.IsNullOrEmpty(userLanguage))
                {
                    var otpEnglishTemplate = OTPTemplate();
                    otpEnglishTemplate = otpEnglishTemplate.Replace("{{Code}}", code).Replace("{{userEmail}}", userEmail);
                    List<string> emailTosend = new List<string>
                                {
                                    userEmail
                                };
                    await SESService.SendOTPMail(emailTosend, otpEnglishTemplate, "<EMAIL>", "Verify Mail");
                }
                else
                {

                    var locale = userLanguage.Split('-')[0].ToLower();
                    string otpTemplate = OTPTemplateToTranslate();
                    var getSentenceToTranslate = StringToTranslateOTP();
                    var translatedSentence = await translate.TranslateOTP(locale, getSentenceToTranslate);
                    var translatedTemplate = ReplaceTranslatedSentenceToOtpTEmplate(translatedSentence, otpTemplate);
                    translatedTemplate = translatedTemplate.Replace("{{Code}}", code).Replace("{{userEmail}}", userEmail);
                    List<string> emailTosend = new List<string>
                                {
                                    userEmail
                                };
                    await SESService.SendOTPMail(emailTosend, translatedTemplate, "<EMAIL>", "Verify Mail");



                }



            }
            catch (Exception ex)
            {
                return BadRequest();
            }
            return Ok();
        }

        public string GenerateOtp()
        {
            Random random = new Random();
            int otp = random.Next(100000, 999999);
            return otp.ToString();
        }
        public record UserOtpPayload(
            Guid userId,
            string code);

        public record UserOtpVerifyPayload(
            string userEmail,
            string code,
            Guid deviceId
            , Guid deviceTypeId,
            string DeviceData = "");

        public class OTPReturnModel
        {
            public string message { get; set; }

            public Guid tokenId { get; set; }

            public string redirectUrl { get; set; }
            public Guid userId { get; set; }
        }

        [Route("73FE9D70")]
        [HttpPost]

        public async Task<IActionResult> ValidateUserOTP([FromBody] UserOtpPayload payload)
        {
            OTPReturnModel returnModel = new OTPReturnModel();
            var userOtp = await wepdb.Users.Where(w => w.Id == payload.userId).Select(w => w.Otp).FirstOrDefaultAsync();
            // var otpSent = _redisCaching.GetData<string>("userInfo_otp" + payload.userId);
            // if (otpSent == null)
            // {
            //     returnModel.message = "OTP timeout refresh to get new otp";
            //     return BadRequest(returnModel);
            // }
            if (userOtp == payload.code)
            {
                returnModel.message = "Success";
                var isUserSettingPresent = await wepdb.UserSettings.Where(x => x.UserId == payload.userId && x.SettingId == CommonData.ISEmailValidated).FirstOrDefaultAsync();
                if (isUserSettingPresent == null)
                {
                    UserSetting userSetting = new UserSetting();
                    userSetting.Id = Guid.NewGuid();
                    userSetting.UserId = payload.userId;
                    userSetting.SettingId = CommonData.ISEmailValidated;
                    userSetting.Value = "1";
                    userSetting.CreatedDate = DateTime.Now;
                    userSetting.ModifiedDate = DateTime.Now;
                    wepdb.UserSettings.Add(userSetting);
                    await wepdb.SaveChangesAsync();
                }
                else
                {
                    isUserSettingPresent.Value = "1";
                    await wepdb.SaveChangesAsync();
                }
                return Ok(returnModel);
            }
            returnModel.message = "Wrong OTP";
            return BadRequest(returnModel);
        }

        [Route("7ACFC032")]
        [HttpPost]
        public async Task<IActionResult> HandlePlanForTeamsForFirstTime(Guid deviceId)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                var user = await wepdb.Users.AsNoTracking().FirstOrDefaultAsync(w => w.Id == userId);
                var isTeamsUser = await wepdb.UserDevices.AsNoTracking().AnyAsync(w => w.UserId == userId && w.Id == deviceId && w.DeviceTypeId == CommonData.TeamsDeviceTypeId);
                if (isTeamsUser)
                {
                    // start trial 
                    var userTrialSetting = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == userId && w.SettingId == CommonData.UserTrialStartDateSettingId);
                    if (userTrialSetting != null)
                    {
                        // trial starts from now
                        userTrialSetting.Value = DateTime.UtcNow.ToString();
                        userTrialSetting.ModifiedDate = DateTime.UtcNow;
                        await wepdb.SaveChangesAsync();
                    }
                    var trialDaysSetting = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == userId && w.SettingId == CommonData.UserTrialDaysSettingId);
                    if (trialDaysSetting != null)
                    {
                        int trialDays = int.Parse(trialDaysSetting.Value.ToString());
                        // create signal to end the trial
                        Signal signal = new Signal()
                        {
                            UserId = userId,
                            CreatedDate = DateTime.UtcNow,
                            Id = Guid.NewGuid(),
                            IsActive = true,
                            IsComplete = false,
                            IsProcessing = false,
                            IsSeen = false,
                            Iteration = 0,
                            ModifiedDate = DateTime.UtcNow,
                            SignalTypeId = CommonData.TrialStatusUpdateSignalTypeId,
                            TriggerOn = DateTime.UtcNow.AddDays(trialDays)
                        };
                        wepdb.Signals.Add(signal);
                        await wepdb.SaveChangesAsync();
                    }
                    // create user plan
                    var checkForUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.PlanId == CommonData.SalespersonPland && w.UserId == user.Id && w.IsActive == true);
                    if (!checkForUserPlan)
                    {
                        // checkForOtherUserPlam
                        var existingOtherPlans = await wepdb.UserPlans.Where(w => w.UserId == user.Id && w.PlanId != CommonData.SalespersonPland && w.IsActive == true).ToListAsync();
                        if (existingOtherPlans.Count > 0)
                        {
                            foreach (var existingPlan in existingOtherPlans)
                            {
                                existingPlan.IsActive = false;
                                existingPlan.ModifiedDate = DateTime.UtcNow;
                                await wepdb.SaveChangesAsync();
                            }
                        }
                        UserPlan userPlan = new UserPlan()
                        {
                            Id = Guid.NewGuid(),
                            CreatedDate = DateTime.UtcNow,
                            IsActive = true,
                            ModifiedDate = DateTime.UtcNow,
                            PlanId = CommonData.SalespersonPland,
                            UserId = user.Id
                        };
                        wepdb.UserPlans.Add(userPlan);
                        await wepdb.SaveChangesAsync();
                    }
                    else
                    {
                        var userPlanData = await wepdb.UserPlans.FirstOrDefaultAsync(w => w.PlanId == CommonData.SalespersonPland && w.UserId == user.Id && w.IsActive == true);
                        userPlanData.IsActive = true;
                        userPlanData.ModifiedDate = DateTime.UtcNow;
                        await wepdb.SaveChangesAsync();
                    }

                    await UpdateForCredits(user);
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }
        public async Task UpdateForCredits(User user)
        {
            try
            {
                var EmailSplit = user.Email.Split('@');
                var domain = EmailSplit[1].ToLower();
                var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(w => w.DomainName.ToLower() == domain.ToLower());
                if (isFreeDomain)
                {
                    Guid userId = user.Id;
                    var isCreditPresent = await wepdb.UserSettings.Where(x => x.UserId == userId && x.SettingId == CommonData.NonVanishingCreditSettingId).FirstOrDefaultAsync();
                    if (isCreditPresent != null)
                    {
                        isCreditPresent.Value = "20";
                        isCreditPresent.ModifiedDate = DateTime.UtcNow;
                        await wepdb.SaveChangesAsync();
                    }
                    else
                    {
                        UserSetting creditSetting = new UserSetting();
                        creditSetting.Id = Guid.NewGuid();
                        creditSetting.UserId = userId;
                        creditSetting.SettingId = CommonData.VanishingCreditSettingId;
                        creditSetting.CreatedDate = DateTime.UtcNow;
                        creditSetting.Value = "20";
                        creditSetting.ModifiedDate = DateTime.UtcNow;
                        creditSetting.IsActive = true;
                        creditSetting.IsUserUpdated = true;
                        creditSetting.IsAdminUpdated = true;
                        wepdb.UserSettings.Add(creditSetting);
                        await wepdb.SaveChangesAsync();
                    }
                    CreditUsage creditUsage = new CreditUsage();
                    creditUsage.Id = Guid.NewGuid();
                    creditUsage.UserId = userId;
                    creditUsage.RedeemDateTime = DateTime.UtcNow;
                    creditUsage.Credit = "20";
                    creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                    creditUsage.IsCredited = true;
                    weodb.CreditUsages.Add(creditUsage);
                    await weodb.SaveChangesAsync();
                }
                else
                {
                    var org = await weodb.Orgs.AsNoTracking().Where(x => x.Name.ToLower() == domain.ToLower() && x.AppId.ToLower() == CommonData.GlobalAppId.ToString().ToLower() && x.IsActive == true).AsNoTracking().FirstOrDefaultAsync();
                    if (org != null)
                    {
                        var orgCreditUsage = await weodb.OrgCreditsNews.Where(x => x.OrgId == org.Id).FirstOrDefaultAsync();
                        if (orgCreditUsage != null)
                        {
                            int creditsToUpdate = int.Parse(orgCreditUsage.Credits) + 20;
                            orgCreditUsage.Credits = creditsToUpdate.ToString();
                            await weodb.SaveChangesAsync();

                            CreditUsage creditUsage = new CreditUsage();
                            creditUsage.Id = Guid.NewGuid();
                            creditUsage.UserId = user.Id;
                            creditUsage.RedeemDateTime = DateTime.UtcNow;
                            creditUsage.Credit = "20";
                            creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                            creditUsage.IsCredited = true;
                            weodb.CreditUsages.Add(creditUsage);
                            await weodb.SaveChangesAsync();
                        }
                        else
                        {
                            OrgCreditsNew orgCredits = new OrgCreditsNew()
                            {
                                Credits = "20",
                                Id = Guid.NewGuid(),
                                LastPurchaseDate = DateTime.UtcNow,
                                OrgId = org.Id
                            };
                            weodb.OrgCreditsNews.Add(orgCredits);
                            await weodb.SaveChangesAsync();

                            CreditUsage creditUsage = new CreditUsage();
                            creditUsage.Id = Guid.NewGuid();
                            creditUsage.UserId = user.Id;
                            creditUsage.RedeemDateTime = DateTime.UtcNow;
                            creditUsage.Credit = "20";
                            creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                            creditUsage.IsCredited = true;
                            weodb.CreditUsages.Add(creditUsage);
                            await weodb.SaveChangesAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        [HttpPost]
        [Route("249E6CD2")]

        public async Task<IActionResult> SendSSEForDrop(int dropId, string userEmail)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                var appUrl = _configuration["ServerSettings:appUrl"];
                var userInfo = _redisCaching.CheckForItem("userDetailInfo_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CommonData.CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddHours(1));
                var userLanguage = await wepdb.UserSettings.Where(x => x.UserId == userInfo.Id && x.SettingId == CommonData.SelectedLanguageSettingId).Select(x => x.Value).FirstOrDefaultAsync();

                switch (dropId)
                {
                    case 1:
                        {
                            string url = $"{appUrl}co?cal=1";
                            if (userLanguage.Contains("en") || string.IsNullOrEmpty(userLanguage))
                            {
                                string dropbox1Template = SendDrop1and2Templte();


                                string replacedTemplate = TemplateReplace(dropbox1Template, userInfo.FirstName, url);
                                List<string> emailTosend = new List<string>
                                {
                                    userEmail
                                };
                                await SESService.SendDropMail(emailTosend, replacedTemplate, "<EMAIL>", "1Page registration");
                            }
                            else
                            {
                                var locale = userLanguage.Split('-')[0].ToLower();
                                string dropbox1Template = SendDrop1and2TranslateTemplte();
                                var translatedWords = "";
                                var redisTemplate = _redisCaching.GetData<string>("translatedTemlate" + locale + "DropBox1");
                                redisTemplate = "";
                                if (!string.IsNullOrEmpty(redisTemplate))
                                {
                                    translatedWords = redisTemplate;
                                }
                                else
                                {
                                    translatedWords = await translate.TranslateTheDropMail(1, locale);
                                    _redisCaching.SetData("translatedTemlate" + locale + "DropBox1", translatedWords, DateTimeOffset.Now.AddDays(30));
                                }
                                var finalResult = ReplaceTranslateMailDrop1(translatedWords, dropbox1Template);
                                finalResult = finalResult.Replace("###", userInfo.FirstName).Replace("####", url);
                                List<string> emailTosend = new List<string>
                                {
                                    userEmail
                                };
                                await SESService.SendDropMail(emailTosend, finalResult, "<EMAIL>", "1Page registration");

                            }

                        }
                        break;
                    case 2:
                        {
                            string url = $"{appUrl}co?cal=2";
                            //userLanguage = "es";
                            if (userLanguage.Contains("en") || string.IsNullOrEmpty(userLanguage))
                            {
                                string dropbox1Template = SendDrop1and2Templte();

                                string replacedTemplate = TemplateReplace(dropbox1Template, userInfo.FirstName, url);
                                List<string> emailTosend = new List<string>
                                {
                                    userEmail
                                };
                                await SESService.SendDropMail(emailTosend, replacedTemplate, "<EMAIL>", "1Page registration");
                            }
                            else
                            {

                                var locale = userLanguage.Split('-')[0].ToLower();
                                string dropbox1Template = SendDrop1and2TranslateTemplte();
                                var translatedWords = "";
                                var redisTemplate = _redisCaching.GetData<string>("translatedTemlate" + locale + "DropBox1");
                                if (!string.IsNullOrEmpty(redisTemplate))
                                {
                                    translatedWords = redisTemplate;
                                }
                                else
                                {
                                    translatedWords = await translate.TranslateTheDropMail(1, locale);
                                    _redisCaching.SetData("translatedTemlate" + locale + "DropBox1", translatedWords, DateTimeOffset.Now.AddDays(30));
                                }
                                var finalResult = ReplaceTranslateMailDrop1(translatedWords, dropbox1Template);
                                finalResult = finalResult.Replace("###", userInfo.FirstName).Replace("####", url);
                                List<string> emailTosend = new List<string>
                                {
                                    userEmail
                                };
                                await SESService.SendDropMail(emailTosend, finalResult, "<EMAIL>", "1Page registration");

                            }


                        }
                        break;
                    case 3:
                        {
                            string url = $"{appUrl}pricing?sub=1";
                            if (userLanguage.Contains("en") || string.IsNullOrEmpty(userLanguage))
                            {
                                string dropbox1Template = Drop3Template();

                                string replacedTemplate = TemplateReplace(dropbox1Template, userInfo.FirstName, url);
                                List<string> emailTosend = new List<string>
                     {
                         userEmail
                     };
                                await SESService.SendDropMail(emailTosend, replacedTemplate, "<EMAIL>", "1Page registration");

                            }
                            else
                            {

                                var locale = userLanguage.Split('-')[0].ToLower();
                                string dropbox1Template = SendDrop3TranslateTemplte();
                                var translatedWords = "";
                                var redisTemplate = _redisCaching.GetData<string>("translatedTemlate" + locale + "DropBox3");
                                if (!string.IsNullOrEmpty(redisTemplate))
                                {
                                    translatedWords = redisTemplate;
                                }
                                else
                                {
                                    translatedWords = await translate.TranslateTheDropMail(1, locale);
                                    _redisCaching.SetData("translatedTemlate" + locale + "DropBox3", translatedWords, DateTimeOffset.Now.AddDays(30));
                                }
                                var finalResult = ReplaceTranslateMailDrop1(translatedWords, dropbox1Template);
                                finalResult = finalResult.Replace("###", userInfo.FirstName).Replace("####", url);
                                List<string> emailTosend = new List<string>
                     {
                         userEmail
                     };
                                await SESService.SendDropMail(emailTosend, finalResult, "<EMAIL>", "1Page registration");

                            }
                        }

                        break;
                }
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
            return Ok();
        }

        public string ReplaceTranslateMailDrop1(string translatedScript, string translatedTemplate)
        {
            var traslatedWords = translatedScript.Split('$');
            int i = 0;
            foreach (var traslatedWord in traslatedWords)
            {
                translatedTemplate = translatedTemplate.Replace("{{" + i + "}}", traslatedWord);
                i++;
            }
            return translatedTemplate;
        }

        public string ReplaceTranslatedSentenceToOtpTEmplate(string translatedScript, string translatedTemplate)
        {
            var traslatedWords = translatedScript.Split('$');
            int i = 0;
            foreach (var traslatedWord in traslatedWords)
            {
                translatedTemplate = translatedTemplate.Replace("{" + i + "}", traslatedWord);
                i++;
            }
            translatedTemplate = translatedTemplate.Replace("###", "1Page");
            return translatedTemplate;

        }

        public string SendDrop3TranslateTemplte()
        {
            return @"<!DOCTYPE html>
         <html lang=""""en"""">
         <head>
             <meta charset=""""UTF-8"""">
             <meta name=""""viewport"""" content=""""width=device-width, initial-scale=1.0"""">
             <title>Welcome to Moukha!</title>
             <style>
                 body {
                     font-family: 'Arial', sans-serif;
                     line-height: 1.6;
                     margin: 0;
                     padding: 0;
                 }

                 .container {
                     max-width: 600px;
                     margin: 20px auto;
                     text-align: center;
                 }
                 .text{
                 text-align: left;
                 }
                 .button-center{
                 text-align: center;
                 }

                 h1 {
                     color: #333;
                 }

                 p {
                     color: #555;
                 }

                 .button {
                    background-color: #008CBA; 
                    border: none;
                  color: white;
                padding: 15px 32px;
                  text-align: center;
                   text-decoration: none;
                  display: inline-block;
                     font-size: 16px;
                  margin: 4px 2px;
                  cursor: pointer;
 
                      }
         </style>
         </head>
         <body>
             <div >
                 
                 <p><strong>{{0}} ###,</strong></p>                        <br>
                 <p class=""text"">{{1}}</p>
                                         <p class=""text"">{{2}}</p>
                 
                    <p class=""text"">{{3}}</p>
                    
                    <p class=""text"">{{4}}</p>
                    
                 <div class =""button-center"">
                 <a href=####><button class=""button"">{{5}}</button></a>
                 </div>
                 <br>
                 <p>{{6}}</p>
                 <p>1Page Team</p>
                    
             </div>
         </body>
         </html>";
        }
        public string SendDrop1and2TranslateTemplte()
        {
            return @"<!DOCTYPE html>
         <html lang=""""en"""">
         <head>
             <meta charset=""""UTF-8"""">
             <meta name=""""viewport"""" content=""""width=device-width, initial-scale=1.0"""">
             <title>Welcome to Moukha!</title>
             <style>
                 body {
                     font-family: 'Arial', sans-serif;
                     line-height: 1.6;
                     margin: 0;
                     padding: 0;
                 }

                 .container {
                     max-width: 600px;
                     margin: 20px auto;
                     text-align: center;
                 }
                 .text{
                 text-align: left;
                 }
                 .button-center{
                 text-align: center;
                 }

                 h1 {
                     color: #333;
                 }

                 p {
                     color: #555;
                 }

                 .button {
                    background-color: #008CBA; 
                    border: none;
                  color: white;
                padding: 15px 32px;
                  text-align: center;
                   text-decoration: none;
                  display: inline-block;
                     font-size: 16px;
                  margin: 4px 2px;
                  cursor: pointer;
 
                      }
         </style>
         </head>
         <body>
             <div >
                 
                 <p><strong>{{0}} ###,</strong></p>                        <br>
                 <p class=""text"">{{1}}</p>
                                         <p class=""text"">{{2}}</p>
                 
                    <p class=""text"">{{3}}</p>
                    
                    <p class=""text"">{{4}}</p>
                    
                 
                 <a href=####><button class=""button"">{{5}}</button></a>
                
                 <br>
                 <p>{{6}}</p>
                 <p>1Page Team</p>
                    
             </div>
         </body>
         </html>";
        }
        public string StringToTranslateOTP()
        {
            return "Hi $ We received your request for a single-use code to use with your ### account. $ Your single-use code is $ If you didnt request this code, you can safely ignore this email. Someone else might have typed your email address by mistake. $ Thanks, $ Privacy Statement";

        }

        [HttpPost]
        [Route("A2192DDE")]
        [AllowAnonymous]
        public async Task<IActionResult> SendOtPDuringVerify([FromQuery] string userEmail, string userLanguage)
        {
            try
            {
                bool isValidEmail = await VerifyEmailByKickBoxMI(userEmail);
                if (isValidEmail == false)
                {
                    return BadRequest("Wrong EmailId");
                }
                var code = GenerateOtp();
                _redisCaching.SetDataSliding<string>("userVerify_otp_in_Email" + userEmail, code, TimeSpan.FromMinutes(10));


                //userLanguage = "es";
                if (userLanguage.Contains("en") || string.IsNullOrEmpty(userLanguage))
                {
                    var otpEnglishTemplate = OTPTemplate();
                    otpEnglishTemplate = otpEnglishTemplate.Replace("{{Code}}", code).Replace("{{userEmail}}", userEmail);
                    List<string> emailTosend = new List<string>
                                {
                                    userEmail
                                };
                    await SESService.SendOTPMail(emailTosend, otpEnglishTemplate, "<EMAIL>", "Verify Mail");
                }
                else
                {

                    var locale = userLanguage.Split('-')[0].ToLower();
                    string otpTemplate = OTPTemplateToTranslate();
                    var getSentenceToTranslate = StringToTranslateOTP();
                    var translatedSentence = await translate.TranslateOTP(locale, getSentenceToTranslate);
                    var translatedTemplate = ReplaceTranslatedSentenceToOtpTEmplate(translatedSentence, otpTemplate);
                    translatedTemplate = translatedTemplate.Replace("{{Code}}", code).Replace("{{userEmail}}", userEmail);
                    List<string> emailTosend = new List<string>
                                {
                                    userEmail
                                };
                    await SESService.SendOTPMail(emailTosend, translatedTemplate, "<EMAIL>", "Verify Mail");



                }
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

            return Ok();

        }

        [HttpPost]
        [AllowAnonymous]
        [Route("B973E432")]
        public async Task<IActionResult> OTpValidateforverify([FromBody] UserOtpVerifyPayload payload)
        {
            OTPReturnModel returnModel = new OTPReturnModel();
            try
            {

                var otpSent = _redisCaching.GetData<string>("userVerify_otp_in_Email" + payload.userEmail);
                if (otpSent == null)
                {
                    returnModel.message = "OTP timeout refresh to get new otp";
                    return BadRequest(returnModel);
                }

                if (otpSent == payload.code)
                {
                    returnModel.message = "Success";
                    var isUserPresent = await wepdb.Users.AsNoTracking().Where(x => x.Email == payload.userEmail && x.AppId == CommonData.GlobalAppId).FirstOrDefaultAsync();
                    if (isUserPresent == null)
                    {

                        Guid userId2 = Guid.NewGuid();
                        User user = new User
                        {
                            Id = userId2,
                            Password = payload.deviceId.ToString() + "|1",
                            CountryId = "IN",
                            LastLogin = DateTime.UtcNow,
                            PreviousLogin = DateTime.UtcNow,
                            FirstLogin = DateTime.UtcNow,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            FirstName = payload.userEmail.Split('@')[0],
                            LastName = "",
                            IsDisabled = false,
                            IsUser = true,
                            IsActive = true,
                            Notes = "",
                            AppId = CommonData.GlobalAppId,
                            LanguageId = 1,
                            Email = payload.userEmail,
                            IsEmailValidated = false,
                            AnalyticsId = Guid.NewGuid(),
                            IsTestUser = false
                        };
                        wepdb.Users.Add(user);
                        await wepdb.SaveChangesAsync();
                        SetUserSettings(user.Id);
                        //await allSetUserSettings(user.Id);
                        Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = user.AppId,
                            UserAgent = "",
                            UserDeviceId = payload.deviceId
                        };
                        Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = user.AppId,
                            UserAgent = "",
                            UserDeviceId = payload.deviceId,
                            IsWeb = true
                        };
                        Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = user.AppId,
                            UserAgent = "",
                            Details = "Webhook",
                            UserDeviceId = payload.deviceId,
                            IsWeb = false
                        };
                        wetdb.Tokens.Add(webtoken);
                        wetdb.Tokens.Add(webhooktoken);
                        wetdb.Tokens.Add(token);
                        await wetdb.SaveChangesAsync();
                        UpdateOtherTablesUsingEmailForVerify(payload, payload.userEmail, user, token);

                        var split = user.Email.Split('@');
                        var OrgUsers = await weodb.OrgUsers.Where(x => x.UserId == user.Id && x.IsActive && x.IsActivated == true).Include(x => x.Org).ToListAsync();

                        var OrgId = await weodb.OrgUsers.AsNoTracking().Where(x => x.Org.Name.ToLower() == split[1].ToLower() && x.Org.AppId == CommonData.GlobalAppId.ToString()).Select(x => x.OrgId).FirstOrDefaultAsync();


                        var org = await weodb.Orgs.AsNoTracking().Where(w => w.Id == OrgId).FirstOrDefaultAsync();
                        bool isTeamsUser = false;
                        if (!string.IsNullOrEmpty(user.Email))
                        {
                            var domain = user.Email.Split('@')[1];
                            if (domain == "onmicrosoft.com")
                            {
                                return BadRequest(CommonData.ErrorCodes.E0004);
                            }
                        }
                        var dTypeId = payload.deviceTypeId.ToString().ToLower();
                        if (dTypeId == "467aae32-8361-43a3-a8c7-be21e32c4f3b")
                            isTeamsUser = true;
                        else
                            isTeamsUser = false;
                        string redirectUrl = "";
                        if (isTeamsUser)
                        {
                            if (org.IsPaid == true && OrgUsers.Count > 0)
                                redirectUrl = await weadb.DeploymentUrls.AsNoTracking().Where(w => w.IsPaid == org.IsPaid && w.OrgId == OrgId && w.ServerType == 2).Select(x => x.RedirectUrl).FirstOrDefaultAsync();
                            else
                                redirectUrl = await weadb.DeploymentUrls.AsNoTracking().Where(w => w.IsPaid == org.IsPaid && w.OrgId == CommonData.WhatElseCustomerOrgId && w.ServerType == 2).Select(x => x.RedirectUrl).FirstOrDefaultAsync();
                        }
                        else
                        {
                            if (org.IsPaid == true && OrgUsers.Count > 0)
                            {
                                redirectUrl = await weadb.DeploymentUrls.AsNoTracking().Where(w => w.IsPaid == true && w.OrgId == OrgId && w.ServerType == 1).Select(x => x.RedirectUrl).FirstOrDefaultAsync();
                            }
                            else if (org.IsPaid == true && OrgUsers.Count <= 0)
                            {
                                redirectUrl = await weadb.DeploymentUrls.AsNoTracking().Where(w => w.IsPaid == false && w.OrgId == CommonData.WhatElseCustomerOrgId && w.ServerType == 1).Select(x => x.RedirectUrl).FirstOrDefaultAsync();
                            }
                            else
                            {
                                org.IsPaid = false;
                                redirectUrl = await weadb.DeploymentUrls.AsNoTracking().Where(w => w.IsPaid == org.IsPaid && w.OrgId == CommonData.WhatElseCustomerOrgId && w.ServerType == 1).Select(x => x.RedirectUrl).FirstOrDefaultAsync();
                            }
                        }
                        //   var checkForAppSumo = await wepdb.UserCoupons.AnyAsync(w => w.EmailId.ToLower() == user.Email.ToLower() && w.IsRedeemed == true && w.IsApplied == false && w.IsActive == true);
                        returnModel.redirectUrl = redirectUrl;
                        returnModel.tokenId = token.Id;
                        //if (checkForAppSumo)
                        //{
                        //    try
                        //    {
                        //        var couponData = await wepdb.UserCoupons.FirstOrDefaultAsync(w => w.EmailId.ToLower() == user.Email.ToLower() && w.IsRedeemed == true && w.IsApplied == false && w.IsActive == true);
                        //        couponData.IsApplied = true;
                        //        couponData.ModifiedDate = DateTime.UtcNow;
                        //        await wepdb.SaveChangesAsync();

                        //        // adding credits for user for org
                        //        var checkForPlan = await weadb.CouponNews.FirstOrDefaultAsync(w => w.Id == couponData.CouponId);
                        //        var offerData = await weadb.Offers.FirstOrDefaultAsync(w => w.Id == checkForPlan.OfferId);
                        //        var orgDomain = split[1];
                        //        var orgDetail = weodb.Orgs.Where(x =>
                        //            x.Name.ToLower() == orgDomain.ToLower() && x.IsActive == true &&
                        //            x.AppId.ToLower() == user.AppId.ToString().ToLower()).FirstOrDefault();
                        //        var creditsForOrg = await weodb.OrgCreditsNews.FirstOrDefaultAsync(x => x.OrgId == orgDetail.Id);
                        //        if (creditsForOrg != null)
                        //        {
                        //            if (creditsForOrg.Credits == null)
                        //                creditsForOrg.Credits = "0";
                        //            var redeemedCredits = int.Parse(creditsForOrg.Credits) + int.Parse(offerData.Credits);
                        //            creditsForOrg.Credits = redeemedCredits.ToString();
                        //            await weodb.SaveChangesAsync();
                        //        }

                        //        // add the userPlan
                        //        var checkForUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.PlanId == offerData.Id && w.UserId == user.Id && w.IsActive == true);
                        //        if (!checkForUserPlan)
                        //        {
                        //            // checkForOtherUserPlam
                        //            var existingOtherPlans = await wepdb.UserPlans.Where(w => w.UserId == user.Id && w.PlanId != offerData.Id && w.IsActive == true).ToListAsync();
                        //            if (existingOtherPlans.Count > 0)
                        //            {
                        //                foreach (var existingPlan in existingOtherPlans)
                        //                {
                        //                    existingPlan.IsActive = false;
                        //                    existingPlan.ModifiedDate = DateTime.UtcNow;
                        //                    await wepdb.SaveChangesAsync();
                        //                }
                        //            }
                        //            UserPlan userPlan = new UserPlan()
                        //            {
                        //                Id = Guid.NewGuid(),
                        //                CreatedDate = DateTime.UtcNow,
                        //                IsActive = true,
                        //                ModifiedDate = DateTime.UtcNow,
                        //                PlanId = offerData.Id,
                        //                UserId = user.Id
                        //            };
                        //            wepdb.UserPlans.Add(userPlan);
                        //            await wepdb.SaveChangesAsync();
                        //        }
                        //        else
                        //        {
                        //            var userPlanData = await wepdb.UserPlans.FirstOrDefaultAsync(w => w.PlanId == offerData.Id && w.UserId == user.Id && w.IsActive == true);
                        //            userPlanData.IsActive = true;
                        //            userPlanData.ModifiedDate = DateTime.UtcNow;
                        //            await wepdb.SaveChangesAsync();
                        //        }


                        //    }
                        //    catch (Exception ex)
                        //    {

                        //    }
                        //}

                        return Ok(returnModel);
                    }
                    else
                    {
                        var user = await wepdb.Users.Where(x => x.Id == isUserPresent.Id).FirstOrDefaultAsync();
                        user.Password = $"{payload.deviceId.ToString().ToLower()}|1";
                        await wepdb.SaveChangesAsync();
                        SetUserSettings(user.Id);
                        //await allSetUserSettings(user.Id);
                        Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = user.AppId,
                            UserAgent = "",
                            UserDeviceId = payload.deviceId
                        };
                        Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = user.AppId,
                            UserAgent = "",
                            UserDeviceId = payload.deviceId,
                            IsWeb = true
                        };
                        Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = user.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = user.AppId,
                            UserAgent = "",
                            Details = "Webhook",
                            UserDeviceId = payload.deviceId,
                            IsWeb = false
                        };
                        wetdb.Tokens.Add(webtoken);
                        wetdb.Tokens.Add(webhooktoken);
                        wetdb.Tokens.Add(token);
                        wetdb.SaveChanges();
                        UpdateOtherTablesUsingEmailForVerify(payload, payload.userEmail, user, token);

                        var split = user.Email.Split('@');
                        var OrgUsers = weodb.OrgUsers.Where(x => x.UserId == user.Id && x.IsActive && x.IsActivated == true).Include(x => x.Org).ToList();

                        var OrgId = weodb.OrgUsers.Where(x => x.Org.Name.ToLower() == split[1].ToLower() && x.Org.AppId == CommonData.GlobalAppId.ToString()).Select(x => x.OrgId).FirstOrDefault();


                        var org = weodb.Orgs.FirstOrDefault(w => w.Id == OrgId);
                        bool isTeamsUser = false;
                        if (!string.IsNullOrEmpty(user.Email))
                        {
                            var domain = user.Email.Split('@')[1];
                            if (domain == "onmicrosoft.com")
                            {
                                return BadRequest(CommonData.ErrorCodes.E0004);
                            }
                        }
                        var dTypeId = payload.deviceTypeId.ToString().ToLower();
                        if (dTypeId == "467aae32-8361-43a3-a8c7-be21e32c4f3b")
                            isTeamsUser = true;
                        else
                            isTeamsUser = false;
                        string redirectUrl = "";
                        if (isTeamsUser)
                        {
                            if (org.IsPaid == true && OrgUsers.Count > 0)
                                redirectUrl = weadb.DeploymentUrls.FirstOrDefault(w => w.IsPaid == org.IsPaid && w.OrgId == OrgId && w.ServerType == 2).RedirectUrl;
                            else
                                redirectUrl = weadb.DeploymentUrls.FirstOrDefault(w => w.IsPaid == org.IsPaid && w.OrgId == CommonData.WhatElseCustomerOrgId && w.ServerType == 2).RedirectUrl;
                        }
                        else
                        {
                            if (org.IsPaid == true && OrgUsers.Count > 0)
                            {
                                redirectUrl = weadb.DeploymentUrls.FirstOrDefault(w => w.IsPaid == true && w.OrgId == OrgId && w.ServerType == 1).RedirectUrl;
                            }
                            else if (org.IsPaid == true && OrgUsers.Count <= 0)
                            {
                                redirectUrl = weadb.DeploymentUrls.FirstOrDefault(w => w.IsPaid == false && w.OrgId == CommonData.WhatElseCustomerOrgId && w.ServerType == 1).RedirectUrl;
                            }
                            else
                            {
                                org.IsPaid = false;
                                redirectUrl = weadb.DeploymentUrls.FirstOrDefault(w => w.IsPaid == org.IsPaid && w.OrgId == CommonData.WhatElseCustomerOrgId && w.ServerType == 1).RedirectUrl;
                            }
                        }
                        //  var checkForAppSumo = await wepdb.UserCoupons.AnyAsync(w => w.EmailId.ToLower() == user.Email.ToLower() && w.IsRedeemed == true && w.IsApplied == false && w.IsActive == true);
                        returnModel.redirectUrl = redirectUrl;
                        returnModel.tokenId = token.Id;
                        returnModel.userId = user.Id;
                        //if (checkForAppSumo)
                        //{
                        //    try
                        //    {
                        //        var couponData = await wepdb.UserCoupons.FirstOrDefaultAsync(w => w.EmailId.ToLower() == user.Email.ToLower() && w.IsRedeemed == true && w.IsApplied == false && w.IsActive == true);
                        //        couponData.IsApplied = true;
                        //        couponData.ModifiedDate = DateTime.UtcNow;
                        //        await wepdb.SaveChangesAsync();

                        //        // adding credits for user for org
                        //        var checkForPlan = await weadb.CouponNews.FirstOrDefaultAsync(w => w.Id == couponData.CouponId);
                        //        var offerData = await weadb.Offers.FirstOrDefaultAsync(w => w.Id == checkForPlan.OfferId);
                        //        var orgDomain = split[1];
                        //        var orgDetail = weodb.Orgs.Where(x =>
                        //            x.Name.ToLower() == orgDomain.ToLower() && x.IsActive == true &&
                        //            x.AppId.ToLower() == user.AppId.ToString().ToLower()).FirstOrDefault();
                        //        var creditsForOrg = await weodb.OrgCreditsNews.FirstOrDefaultAsync(x => x.OrgId == orgDetail.Id);
                        //        if (creditsForOrg != null)
                        //        {
                        //            if (creditsForOrg.Credits == null)
                        //                creditsForOrg.Credits = "0";
                        //            var redeemedCredits = int.Parse(creditsForOrg.Credits) + int.Parse(offerData.Credits);
                        //            creditsForOrg.Credits = redeemedCredits.ToString();
                        //            await weodb.SaveChangesAsync();
                        //        }

                        //        // add the userPlan
                        //        var checkForUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.PlanId == offerData.Id && w.UserId == user.Id && w.IsActive == true);
                        //        if (!checkForUserPlan)
                        //        {
                        //            // checkForOtherUserPlam
                        //            var existingOtherPlans = await wepdb.UserPlans.Where(w => w.UserId == user.Id && w.PlanId != offerData.Id && w.IsActive == true).ToListAsync();
                        //            if (existingOtherPlans.Count > 0)
                        //            {
                        //                foreach (var existingPlan in existingOtherPlans)
                        //                {
                        //                    existingPlan.IsActive = false;
                        //                    existingPlan.ModifiedDate = DateTime.UtcNow;
                        //                    await wepdb.SaveChangesAsync();
                        //                }
                        //            }
                        //            UserPlan userPlan = new UserPlan()
                        //            {
                        //                Id = Guid.NewGuid(),
                        //                CreatedDate = DateTime.UtcNow,
                        //                IsActive = true,
                        //                ModifiedDate = DateTime.UtcNow,
                        //                PlanId = offerData.Id,
                        //                UserId = user.Id
                        //            };
                        //            wepdb.UserPlans.Add(userPlan);
                        //            await wepdb.SaveChangesAsync();
                        //        }
                        //        else
                        //        {
                        //            var userPlanData = await wepdb.UserPlans.FirstOrDefaultAsync(w => w.PlanId == offerData.Id && w.UserId == userData.Id && w.IsActive == true);
                        //            userPlanData.IsActive = true;
                        //            userPlanData.ModifiedDate = DateTime.UtcNow;
                        //            await wepdb.SaveChangesAsync();
                        //        }


                        //    }
                        //    catch (Exception ex)
                        //    {

                        //    }
                        //}

                        return Ok(returnModel);

                    }
                }
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
            returnModel.message = "Wrong OTP";
            return BadRequest(returnModel);
        }

        async Task allSetUserSettings(Guid userId)
        {
            try
            {
                //Guid firstSetting = Guid.Parse("f4c58170-bd4d-4815-99e2-e40a4d3a0558");

                var settingList = await wepdb.Settings.AsNoTracking().Where(w => w.IsActive == true).ToListAsync();


                foreach (var item in settingList.ToList())
                {

                    try
                    {
                        var exisitngSettings = await wepdb.UserSettings.AsNoTracking().Where(x => x.UserId == userId && x.SettingId == item.Id).FirstOrDefaultAsync();
                        if (exisitngSettings == null)
                        {

                            var vals = item.DefaultValue.Split('|');
                            var userSetting = new UserSetting
                            {
                                Id = Guid.NewGuid(),
                                IsActive = true,
                                SettingId = item.Id,
                                CreatedDate = DateTime.UtcNow,
                                ModifiedDate = DateTime.UtcNow,
                                UserId = userId,
                                DataType = item.DataType,
                                Value = "10"
                            };
                            wepdb.UserSettings.Add(userSetting);
                        }
                        else
                        {


                            var vals = item.DefaultValue.Split('|');




                            exisitngSettings.Value = "10";
                            await wepdb.SaveChangesAsync();




                            await wepdb.SaveChangesAsync();

                        }

                    }
                    catch (Exception ex)
                    {

                        Console.WriteLine(ex.ToString());
                    }
                    await wepdb.SaveChangesAsync();
                }
            }
            catch (Exception)
            {

                throw;
            }

        }


        async Task<bool> VerifyEmailByKickBoxMI(string email)
        {
            try
            {
                sbnew.AppendLine("New UserEmail: " + email);
                var replaceEmail = CommonData.KickboxApi.Replace("{emailaddress}", email);
                var finalApi = replaceEmail.Replace("{kickboxkey}", CommonData.KickboxApiKey);

                using (HttpClient httpClient = new HttpClient())
                {
                    HttpRequestMessage requestMessage = new HttpRequestMessage(System.Net.Http.HttpMethod.Get, finalApi);
                    HttpResponseMessage response = await httpClient.SendAsync(requestMessage);
                    string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                    JToken resultData = JsonConvert.DeserializeObject<JToken>(content);
                    var result = resultData.SelectToken("$.result").Value<string>();
                    sbnew.AppendLine("result: " + result);
                    var desposable = resultData.SelectToken("$.disposable").Value<bool>();
                    sbnew.AppendLine("desposable: " + desposable);
                    var sendex = resultData.SelectToken("$.sendex").Value<double>();
                    sbnew.AppendLine("sendex: " + sendex);
                    var success = resultData.SelectToken("$.success").Value<bool>();
                    sbnew.AppendLine("success: " + success);
                    if (desposable == false && sendex > 0.4 && success == true)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }

            }
            catch (Exception ex)
            {
                //telemetryTracker.TrackException(ex, Guid.Empty);
                return false;
            }
        }

        public string SendDrop1and2Templte()
        {
            return @"<!DOCTYPE html>
         <html lang=""""""""en"""""""">
         <head>
             <meta charset=""""""""UTF-8"""""""">
             <meta name=""""""""viewport"""""""" content=""""""""width=device-width, initial-scale=1.0"""""""">
             <title>Welcome to Moukha!</title>
             <style>
                 body {
                     font-family: 'Arial', sans-serif;
                     line-height: 1.6;
                     margin: 0;
                     padding: 0;
                 }

                 .container {
                     max-width: 600px;
                     margin: 20px auto;
                     text-align: center;
                 }
                 .text{
                 text-align: left;
                 }
                 .button-center{
                 text-align: center;
                 }

                 h1 {
                     color: #333;
                 }

                 p {
                     color: #555;
                 }

                 .button {
                    background-color: #0000; 
                    border: none;
                  color: white;
                padding: 15px 32px;
                  text-align: center;
                   text-decoration: none;
                  display: inline-block;
                     font-size: 16px;
                  margin: 4px 2px;
                  cursor: pointer;
 
                      }
         </style>
         </head>
         <body>
             <div >
                 
                 <p><strong>Hi {{firstName}},</strong></p>                        <br>
                 <p class=""text"">Looks like you could not complete registration</p>
                                         <p class=""text"">We ask you to connect calendar so that we can alert you minutes before every meeting to consistently be prepared for every meeting.</p>
                 
                    <p class=""text"">Alternatively, if you do not want to connect calendar, you can opt for contacts only flow where you can manually add contacts to get insights.</p>
                    
                    <p class=""text"">Lets start where you left off</p>
                    
                
                 <a href={{ContinueReadinglink}}><button class=""""button"""">Continue registration</button></a>
                 
                 <br>
                 <p>Looking forward to you using 1Page,</p>
                 <p>1Page Team</p>
                    
             </div>
         </body>
         </html>";
        }

        public string OTPTemplateToTranslate()
        {
            return @"<!DOCTYPE html>
         <html lang=""""""""""""""""en"""""""""""""""">
         <head>
             <meta charset=""""""""""""""""UTF-8"""""""""""""""">
             <meta name=""""""""""""""""viewport"""""""""""""""" content=""""""""""""""""width=device-width, initial-scale=1.0"""""""""""""""">
            
             <style>
                 body {
                     font-family: 'Arial', sans-serif;
                     line-height: 1.6;
                     margin: 0;
                     padding: 0;
                 }

                

                 h1 {
                     color: #333;
                 }

                 p {
                     color: #555;
                 }

                .text {
                    
                  text-align: center;
                 
                  
 
                      }
                     
                       .horizontal-line {
            width: 100%;
            height: 2px;
            background-color:#888;
            
        }

        </style>
         </head>
         <body>
             <div>
                 
                                      
                 <p>{0} {{userEmail}},</p>
                 
                                         <p >{1}</p>
                 
                    <p>{2}:{{Code}}</p>
                    
                    
                    
                    <p>{3}</p>
              <br>
                 <p>{4}</p>
                 <p>1Page Team</p>
                 <p>{5}:<a href=""https://www.get1page.com/privacy-policy"">https://www.get1page.com/privacy-policy </a></p>
                    
             </div>
         </body>
         </html>";
        }

        public string OTPTemplate()
        {
            return @"<!DOCTYPE html>
         <html lang=""""""""""""""""en"""""""""""""""">
         <head>
             <meta charset=""""""""""""""""UTF-8"""""""""""""""">
             <meta name=""""""""""""""""viewport"""""""""""""""" content=""""""""""""""""width=device-width, initial-scale=1.0"""""""""""""""">
             
             <style>
                 body {
                     font-family: 'Arial', sans-serif;
                     line-height: 1.6;
                     margin: 0;
                     padding: 0;
                 }

                

                 h1 {
                     color: #333;
                 }

                 p {
                     color: #555;
                 }

                .text {
                    
                  text-align: center;
                 
                  
 
                      }
                     
                       .horizontal-line {
            width: 100%;
            height: 2px;
            background-color:#888;
            
        }

        </style>
         </head>
         <body>
             <div>
                 
                                      
                 <p>Hi {{userEmail}},</p>
                 
                                         <p >We received your request for a single-use code to use with your 1Page account.</p>
                 
                    <p>Your single-use code is:{{Code}}</p>
                    
                    
                    
                    <p>If you didnt request to sign in to 1Page, you can safely ignore this email. Someone else might have typed your email address by mistake.</p>
              <br>
                 <p>Thanks,</p>
                 <p>1Page Team</p>
                 <p>Privacy Statement:<a href=""https://www.get1page.com/privacy-policy"">https://www.get1page.com/privacy-policy </a></p>
                    
             </div>
         </body>
         </html>";
        }
        [HttpPost]
        [Route("settingUserid")]

        public IActionResult settingUserid()
        {
            SetUserSettings(Guid.Parse("D7C3A221-FFE9-4673-820E-3093CCA47495"));
            return Ok();
        }
        public string Drop3Template()
        {
            return @"<!DOCTYPE html>
         <html lang=""""""""""""""""en"""""""""""""""">
         <head>
             <meta charset=""""""""""""""""UTF-8"""""""""""""""">
             <meta name=""""""""""""""""viewport"""""""""""""""" content=""""""""""""""""width=device-width, initial-scale=1.0"""""""""""""""">
             <title>Welcome to Moukha!</title>
             <style>
                 body {
                     font-family: 'Arial', sans-serif;
                     line-height: 1.6;
                     margin: 0;
                     padding: 0;
                 }

                 .container {
                     max-width: 600px;
                     margin: 20px auto;
                     text-align: center;
                 }
                 .text{
                 text-align: center;
                 }
                 .button-center{
                 text-align: left;
                 }

                 h1 {
                     color: #333;
                 }

                 p {
                     color: #555;
                 }

                .text {
                    
                  text-align: center;
                 
                  
 
                      }
                     
                       .horizontal-line {
            width: 100%;
            height: 2px;
            background-color:#888;
            
        }

        </style>
         </head>
         <body>
             <div >
                 
                    <p>Your single-use code is:{{Code}}</p>
                    
                    
                    
                    <p>If you didnt request to sign in to 1Page, you can safely ignore this email. Someone else might have typed your email address by mistake.</p>
              <br>
                 <p>Thanks,</p>
                 <p>1Page Team</p>
                 <p>Privacy Statement:<a href=""https://www.get1page.com/privacy-policy"">https://www.get1page.com/privacy-policy </a></p>
                    
             </div>
         </body>
         </html>";
        }

        public string TemplateReplace(string template, string firstName, string url)
        {
            return template.Replace("{{firstName}}", firstName).Replace("{{ContinueReadinglink}}", url);
        }
    }

}
