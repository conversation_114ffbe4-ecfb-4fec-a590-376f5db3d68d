container_commands:
  01_stop_nginx:
    command: "service nginx stop"
    ignoreErrors: true
  02_backup_nginx_configs:
    command: "cp -r /etc/nginx/conf.d /etc/nginx/conf.d.backup"
    ignoreErrors: true
  03_fix_nginx_port_references:
    command: |
      find /etc/nginx -name "*.conf" -type f -exec sed -i 's/127\.0\.0\.1:5000/127.0.0.1:8080/g' {} \;
      find /etc/nginx -name "*.conf" -type f -exec sed -i 's/:5000/:8080/g' {} \;
    ignoreErrors: true
  04_create_custom_upstream:
    command: |
      cat > /etc/nginx/conf.d/01-upstream.conf << 'EOF'
      upstream nodejs {
          server 127.0.0.1:8080;
          keepalive 256;
      }
      EOF
  05_start_nginx:
    command: "service nginx start"
    ignoreErrors: true
