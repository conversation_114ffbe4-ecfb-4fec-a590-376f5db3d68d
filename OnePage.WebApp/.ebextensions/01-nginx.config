files:
  "/etc/nginx/conf.d/dotnet.conf":
    mode: "000644"
    owner: root
    group: root
    content: |
      upstream dotnet {
          server 127.0.0.1:8080;
      }
      
      server {
          listen 80 default_server;
          
          location / {
              proxy_pass http://dotnet;
              proxy_http_version 1.1;
              proxy_set_header Upgrade $http_upgrade;
              proxy_set_header Connection keep-alive;
              proxy_set_header Host $host;
              proxy_set_header X-Real-IP $remote_addr;
              proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header X-Forwarded-Proto $scheme;
              proxy_cache_bypass $http_upgrade;
              
              # Timeout settings
              proxy_connect_timeout 60s;
              proxy_send_timeout 60s;
              proxy_read_timeout 60s;
          }
      }

commands:
  01_remove_default_nginx:
    command: "rm -f /etc/nginx/conf.d/default.conf"
    ignoreErrors: true
  02_reload_nginx:
    command: "service nginx reload"
    ignoreErrors: true
