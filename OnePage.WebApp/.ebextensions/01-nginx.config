files:
  "/etc/nginx/conf.d/elasticbeanstalk/00_application.conf":
    mode: "000644"
    owner: root
    group: root
    content: |
      upstream my_app {
          server 127.0.0.1:8080;
          keepalive 256;
      }

      server {
          listen 80;

          if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2})") {
              set $year $1;
              set $month $2;
              set $day $3;
              set $hour $4;
          }

          access_log /var/log/nginx/healthd/application.log.$year-$month-$day-$hour healthd;

          if ($request_uri ~* "/\.(jpg|jpeg|png|gif|ico|css|js)$") {
              expires 1y;
              add_header Cache-Control public;
              add_header ETag "";
              break;
          }

          location / {
              proxy_pass http://my_app;
              proxy_http_version 1.1;

              proxy_set_header Connection "";
              proxy_set_header Host $host;
              proxy_set_header X-Real-IP $remote_addr;
              proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header X-Forwarded-Proto $scheme;
              proxy_set_header Upgrade $http_upgrade;
              proxy_set_header Connection $connection_upgrade;
          }

          gzip on;
          gzip_comp_level 4;
          gzip_types text/html text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;
      }

container_commands:
  01_reload_nginx:
    command: "service nginx reload"
