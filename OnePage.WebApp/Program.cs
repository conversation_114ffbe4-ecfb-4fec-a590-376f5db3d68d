using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using System.Net;

namespace OnePage.WebApp
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                    var isDevelopment = environment == "Development";

                    // Only use custom port configuration for production/deployment
                    if (!isDevelopment)
                    {
                        webBuilder.ConfigureKestrel(serverOptions =>
                        {
                            var portStr = Environment.GetEnvironmentVariable("PORT") ?? "8080";
                            if (!int.TryParse(portStr, out int port))
                            {
                                port = 8080;
                            }
                            serverOptions.Listen(IPAddress.Any, port, listenOptions =>
                            {
                                listenOptions.Protocols = HttpProtocols.Http1AndHttp2;
                            });
                        });
                    }
                    // For development, use the default configuration from launchSettings.json

                    webBuilder.UseStartup<Startup>();
                });
    }
}
