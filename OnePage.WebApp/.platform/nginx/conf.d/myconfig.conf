# Nginx configuration for OnePage.WebApp
upstream dotnet {
    server 127.0.0.1:8080;
}

server {
    listen 80 default_server;

    # Timeout settings
    client_header_timeout   36000;
    client_body_timeout      36000;
    keepalive_timeout          36000;
    send_timeout                  36000;
    proxy_connect_timeout 36000;
    proxy_read_timeout       36000;
    proxy_send_timeout       36000;
    fastcgi_send_timeout     36000;
    fastcgi_read_timeout      36000;

    # Proxy settings
    location / {
        proxy_pass http://dotnet;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}