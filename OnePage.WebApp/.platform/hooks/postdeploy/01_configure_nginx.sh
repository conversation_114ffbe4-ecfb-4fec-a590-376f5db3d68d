#!/bin/bash

# Configure nginx to proxy to port 8080 instead of 5000
echo "Configuring nginx to proxy to port 8080..."

# Create nginx configuration for .NET Core app
cat > /etc/nginx/conf.d/dotnet.conf << 'EOF'
upstream dotnet {
    server 127.0.0.1:8080;
}

server {
    listen 80 default_server;
    
    location / {
        proxy_pass http://dotnet;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF

# Remove default nginx configuration that might conflict
rm -f /etc/nginx/conf.d/default.conf

# Test nginx configuration
nginx -t

# Reload nginx if configuration is valid
if [ $? -eq 0 ]; then
    echo "Nginx configuration is valid. Reloading nginx..."
    systemctl reload nginx
else
    echo "Nginx configuration is invalid. Please check the configuration."
    exit 1
fi

echo "Nginx configuration completed successfully."
