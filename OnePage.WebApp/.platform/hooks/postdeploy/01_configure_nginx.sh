#!/bin/bash

echo "=== Configuring nginx to proxy to port 8080 instead of 5000 ==="

# Find and replace any references to port 5000 with 8080 in nginx configs
echo "Searching for port 5000 references in nginx configuration..."
find /etc/nginx -name "*.conf" -type f -exec grep -l "5000" {} \; | while read file; do
    echo "Found port 5000 reference in: $file"
    sed -i 's/127\.0\.0\.1:5000/127.0.0.1:8080/g' "$file"
    echo "Updated $file to use port 8080"
done

# Create our custom nginx configuration
echo "Creating custom nginx configuration..."
cat > /etc/nginx/conf.d/dotnet-app.conf << 'EOF'
upstream dotnet_app {
    server 127.0.0.1:8080;
    keepalive 32;
}

server {
    listen 80 default_server;

    location / {
        proxy_pass http://dotnet_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF

# Remove conflicting configurations
echo "Removing conflicting nginx configurations..."
rm -f /etc/nginx/conf.d/default.conf
rm -f /etc/nginx/sites-enabled/default

# Show current nginx configuration files
echo "Current nginx configuration files:"
find /etc/nginx -name "*.conf" -type f | sort

# Test nginx configuration
echo "Testing nginx configuration..."
nginx -t

# Reload nginx if configuration is valid
if [ $? -eq 0 ]; then
    echo "Nginx configuration is valid. Reloading nginx..."
    systemctl reload nginx
    echo "Nginx reloaded successfully."

    # Test the application
    echo "Testing application connectivity..."
    sleep 5
    curl -f http://localhost:8080/statusCheck && echo "✅ Direct app test: SUCCESS" || echo "❌ Direct app test: FAILED"
    curl -f http://localhost/statusCheck && echo "✅ Nginx proxy test: SUCCESS" || echo "❌ Nginx proxy test: FAILED"
else
    echo "❌ Nginx configuration is invalid. Please check the configuration."
    nginx -t
    exit 1
fi

echo "=== Nginx configuration completed ==="
