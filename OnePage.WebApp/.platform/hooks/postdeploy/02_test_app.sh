#!/bin/bash

echo "Testing application endpoints..."

# Wait for application to be ready
sleep 10

# Test if application is running on port 8080
echo "Testing application on port 8080..."
curl -f http://localhost:8080/statusCheck || echo "Direct port 8080 test failed"

# Test through nginx
echo "Testing application through nginx..."
curl -f http://localhost/statusCheck || echo "Nginx proxy test failed"

echo "Application testing completed."
