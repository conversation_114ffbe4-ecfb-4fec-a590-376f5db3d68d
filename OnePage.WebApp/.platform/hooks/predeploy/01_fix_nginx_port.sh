#!/bin/bash

echo "=== Pre-deploy: Fixing nginx port configuration ==="

# Ensure the nginx configuration directory exists
mkdir -p /etc/nginx/conf.d/elasticbeanstalk

# Find and fix any existing nginx configurations that reference port 5000
echo "Searching for and fixing port 5000 references..."
find /etc/nginx -name "*.conf" -type f -exec grep -l "5000" {} \; 2>/dev/null | while read file; do
    echo "Fixing port 5000 reference in: $file"
    sed -i 's/127\.0\.0\.1:5000/127.0.0.1:8080/g' "$file"
    sed -i 's/:5000/:8080/g' "$file"
done

echo "=== Pre-deploy nginx fix completed ==="
