using Microsoft.EntityFrameworkCore.Storage;
using OnePage.Services.Interfaces;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

using IDatabase = StackExchange.Redis.IDatabase;
using System.Linq.Expressions;
using System.Text.Json.Serialization;
//using Serilog;

namespace OnePage.Services
{
    public class RedisCaching : IRedisCaching
    {
        string azureRedisEndpoint = "onepageredis.redis.cache.windows.net";
        string azureRedisAccessKey = "CFgnl7XtchZk2E8N2gp1STUcbcxxwziNZAzCaHKHc9Y=";
        private IDatabase _cacheDb;
        private IDatabase _azureCacheDb;
        string azureRedisConnectionString2 = "onepage.redis.cache.windows.net,password=bIPM9efRd0RGQ9cr832GQOARvUQF09BBXAzCaCgoUus=,ssl=True,abortConnect=False";
        //  string awsRedisEndpoint = "127.0.0.1:6379";


        //   string awsRedisEndpoint = "localhost:6379";

        // string awsRedisEndpoint = "onepageapi.lr4j4h.ng.0001.euw3.cache.amazonaws.com:6379";
        private string awsRedisEndpoint;
        private readonly ConnectionMultiplexer _redisConnection;


        public RedisCaching()
        {
            string connectionString = "valkey-05035796-o7d33919e.database.cloud.ovh.net:20185,ssl=True,user=onepageredis,password=3k2JaGXFOc0j1VQndW7N,abortConnect=False";

            string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            // string awsRedisEndpoint = environment == "Development" ? "127.0.0.1:6379" : "onepage.i3zttz.ng.0001.euw3.cache.amazonaws.com:6379";
            //var options = new ConfigurationOptions
            //{
            //    EndPoints = { connectionString },
            //    AbortOnConnectFail = false
            //};
            //_redisConnection = ConnectionMultiplexer.Connect(options);
            //_cacheDb = _redisConnection.GetDatabase();

            // Configure connection
            var config = ConfigurationOptions.Parse(connectionString);
            config.SslProtocols = System.Security.Authentication.SslProtocols.Tls12 | System.Security.Authentication.SslProtocols.Tls13;
            config.ConnectTimeout = 10000; // 10 seconds
            config.ConnectRetry = 3; // Retry 3 times
            config.SyncTimeout = 10000; // 10 seconds for synchronous operations
            config.AsyncTimeout = 10000; // 10 seconds for asynchronous operations
            // Temporary: Bypass certificate validation for debugging (remove in production)
            config.CertificateValidation += (sender, certificate, chain, sslPolicyErrors) => true;

            try
            {
                // Initialize connection
                _redisConnection = ConnectionMultiplexer.Connect(config);
                _cacheDb = _redisConnection.GetDatabase();
            }
            catch (RedisConnectionException ex)
            {
                throw new InvalidOperationException($"Failed to initialize Valkey connection: {ex.Message}", ex);
            }

            ConnectionMultiplexer connection = ConnectionMultiplexer.Connect(azureRedisConnectionString2);
            _azureCacheDb = connection.GetDatabase();
        }

        public T GetData<T>(string key)
        {
            // var options = new ConfigurationOptions
            // {
            //     EndPoints = { awsRedisEndpoint },
            //     AbortOnConnectFail = false
            // };
            // using (var connection = ConnectionMultiplexer.Connect(options))
            // {
            //     _cacheDb = connection.GetDatabase();
            //     var value = _cacheDb.StringGet(key);
            //     if (!string.IsNullOrEmpty(value))
            //         return JsonSerializer.Deserialize<T>(value);
            // }
            // return default;

            var value = _cacheDb.StringGet(key);
            if (!string.IsNullOrEmpty(value))
                return JsonSerializer.Deserialize<T>(value);
            return default;

        }



        public bool SetDataSliding<T>(string key, T value, TimeSpan slidingExpiration)
        {
            JsonSerializerOptions jsonSerializerOptions = new()
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                WriteIndented = true
            };

            var serializedValue = JsonSerializer.Serialize(value, jsonSerializerOptions);
            return _cacheDb.StringSet(key, serializedValue, slidingExpiration);
            //  return false;
        }

        public T GetDataSliding<T>(string key, TimeSpan slidingExpiration)
        {
            var value = _cacheDb.StringGet(key);
            if (!string.IsNullOrEmpty(value))
            {
                _cacheDb.KeyExpire(key, slidingExpiration);
                return JsonSerializer.Deserialize<T>(value);
            }
            return default;
        }


        public object RemoveData(string key)
        {
            var options = new ConfigurationOptions
            {
                EndPoints = { awsRedisEndpoint },
                AbortOnConnectFail = false
            };
            using (var connection = ConnectionMultiplexer.Connect(options))
            {
                _cacheDb = connection.GetDatabase();
                var _exist = _cacheDb.KeyExists(key);

                if (_exist)
                    return _cacheDb.KeyDelete(key);
            }

            return false;
        }

        public string GetKey(string key)
        {
            var value = _cacheDb.StringGet(key);
            if (!string.IsNullOrEmpty(value))
                return value;
            return default;
        }

        public bool DeleteKey(string key)
        {
            try
            {
                _cacheDb.KeyDelete(key);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }

        }

        public bool SetData<T>(string key, T vaue, DateTimeOffset expirationTime)
        {
            // var options = new ConfigurationOptions
            // {
            //     EndPoints = { awsRedisEndpoint },
            //     AbortOnConnectFail = false
            // };
            JsonSerializerOptions jsonSerializerOptions = new()
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                WriteIndented = true
            };
            // using (var connection = ConnectionMultiplexer.Connect(options))
            // {
            //
            //     _cacheDb = connection.GetDatabase();
            var expiryTime = expirationTime.DateTime.Subtract(DateTime.Now);
            var serializedValue = JsonSerializer.Serialize(vaue, jsonSerializerOptions);
            //     return _cacheDb.StringSet(key, serializedValue, expiryTime);
            //     
            //    
            // }
            //  return false;
            return _cacheDb.StringSet(key, serializedValue, expiryTime);

        }
        public T CheckForItemSliding<T>(string cacheKey, Func<T> retrieveFromSource, Func<T, string> serialize, Func<string, T> deserialize, TimeSpan slidingExpiration)
        {
            try
            {
                var cachedData = GetDataSliding<string>(cacheKey, slidingExpiration);
                if (cachedData != null)
                {
                    if (!cachedData.Contains("{null}"))
                    {
                        if (!string.IsNullOrEmpty(cachedData))
                        {
                            return deserialize(cachedData);
                        }
                    }
                }

                var data = retrieveFromSource();
                var jsonData = serialize(data);
                SetDataSliding(cacheKey, jsonData, slidingExpiration);
                // return data;
                //}
                //catch (Exception e)
                //{
                //    //Log.ForContext("UserEmailId", "")
                //    //        .Error(e, "error from CheckForItemSliding fn");
                //    Console.WriteLine(e);
                //    return default;
                //}
                //  return default;
            }
            catch (Exception e)
            {
                //Log.ForContext("UserEmailId", "")
                //        .Error(e, "error from CheckForItemSliding fn");
                Console.WriteLine(e);
                return default;
            }
            return default;
        }

        public T CheckForItem<T>(string cacheKey, Func<T> retrieveFromSource, Func<T, string> serialize, Func<string, T> deserialize, DateTimeOffset? expiry = null)
        {
            try
            {
                var cachedData = GetData<string>(cacheKey);
                if (cachedData != null && cachedData != "[]" && cachedData != "null")
                {
                    return deserialize(cachedData);
                }


                var data = retrieveFromSource();
                var jsonData = serialize(data);
                SetData(cacheKey, jsonData, expiry ?? DateTimeOffset.Now.AddDays(1));
                return data;
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot2("ex from redis cache " + ex.ToString());
                var data = retrieveFromSource();

                var jsonData = serialize(data);
                SetData(cacheKey, jsonData, expiry ?? DateTimeOffset.Now.AddDays(1));
                return data;
            }
        }
        public T UpdateForItem<T>(string cacheKey, T value)
        {
            var options = new ConfigurationOptions
            {
               EndPoints = { awsRedisEndpoint }
            };
            // using (var connection = ConnectionMultiplexer.Connect(options))
            // {
            // _cacheDb = connection.GetDatabase();
            var data = JsonSerializer.Serialize(value);
          _cacheDb.StringSet(cacheKey, data);
            if (!string.IsNullOrEmpty(data))
                return JsonSerializer.Deserialize<T>(data);
            // }
            return default;
        }

        public T AzureCheckForItem<T>(string cacheKey, Func<T> retrieveFromSource, Func<T, string> serialize, Func<string, T> deserialize, DateTimeOffset? expiry = null)
        {
            var cachedData = GetDataForAzure<string>(cacheKey);
            if (cachedData != null)
            {
               return deserialize(cachedData);
            }

            var data = retrieveFromSource();
            var jsonData = serialize(data);
            SetDataForAzure(cacheKey, jsonData, expiry ?? DateTimeOffset.Now.AddDays(1));
            return data;
            //return default;
        }

        public T GetDataForAzure<T>(string key)
        {
            string azureRedisConnectionString = $"{azureRedisEndpoint},password={azureRedisAccessKey},ssl=True,abortConnect=False";
            ConnectionMultiplexer connection = ConnectionMultiplexer.Connect(azureRedisConnectionString);

            _azureCacheDb = connection.GetDatabase();
            var value = _azureCacheDb.StringGet(key);
            if (!string.IsNullOrEmpty(value))
               return JsonSerializer.Deserialize<T>(value);

            return default;
        }
        public object RemoveDataForAzure(string key)
        {
            string azureRedisConnectionString = $"{azureRedisEndpoint},password={azureRedisAccessKey},ssl=True,abortConnect=False";
            ConnectionMultiplexer connection = ConnectionMultiplexer.Connect(azureRedisConnectionString);

            _cacheDb = connection.GetDatabase();
            var _exist = _cacheDb.KeyExists(key);

            if (_exist)
               return _cacheDb.KeyDelete(key);
            return false;
        }
        public bool SetDataForAzure<T>(string key, T vaue, DateTimeOffset expirationTime)
        {
            string azureRedisConnectionString = $"{azureRedisEndpoint},password={azureRedisAccessKey},ssl=True,abortConnect=False";
            ConnectionMultiplexer connection = ConnectionMultiplexer.Connect(azureRedisConnectionString);

            _azureCacheDb = connection.GetDatabase();
            var expiryTime = expirationTime.DateTime.Subtract(DateTime.Now);
            return _azureCacheDb.StringSet(key, JsonSerializer.Serialize(vaue), expiryTime);
        }
        #region new functions
        // Save a single object with the best approach
        public async Task SaveAsync<T>(string key, T value, TimeSpan? expiry = null)
        {
            // Serialize the object to JSON
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(value, new Newtonsoft.Json.JsonSerializerSettings
            {
               ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore,
               TypeNameHandling = Newtonsoft.Json.TypeNameHandling.Auto
            });

            // Store the serialized object in Redis with an optional expiry time
            await _cacheDb.StringSetAsync(key, json, expiry);
        }

        // Retrieve a single object
        public async Task<T> GetAsync<T>(string key)
        {
            try
            {
               var json = await _cacheDb.StringGetAsync(key);
               if (json.IsNullOrEmpty) return default;

               return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(json, new Newtonsoft.Json.JsonSerializerSettings
               {
                   TypeNameHandling = Newtonsoft.Json.TypeNameHandling.Auto  // Ensures type information is correctly handled
               });
            }
            catch (Exception ex)
            {
               return default;
            }
            return default;
        }

        // Save a list of objects
        public async Task SaveListAsync<T>(string key, List<T> values, TimeSpan? expiry = null)
        {
            try
            {
               // Serialize the object to JSON
               var json = Newtonsoft.Json.JsonConvert.SerializeObject(values, new Newtonsoft.Json.JsonSerializerSettings
               {
                   ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore,
                   TypeNameHandling = Newtonsoft.Json.TypeNameHandling.Auto
               });

               // Store the list in Redis
               await _cacheDb.StringSetAsync(key, json, expiry);
            }
            catch (Exception ex)
            {

            }

        }

       

        // Retrieve a list of objects
        public async Task<List<T>> GetListAsync<T>(string key)
        {
            try
            {
               var json = await _cacheDb.StringGetAsync(key);
               if (json.IsNullOrEmpty) return default;

               return Newtonsoft.Json.JsonConvert.DeserializeObject<List<T>>(json, new Newtonsoft.Json.JsonSerializerSettings
               {
                   TypeNameHandling = Newtonsoft.Json.TypeNameHandling.Auto  // Ensures type information is correctly handled
               });
            }
            catch (Exception ex)
            {
               return default;
            }
            return default;
        }

        // Check if a key exists
        public async Task<bool> ExistsAsync(string key)
        {
            return await _cacheDb.KeyExistsAsync(key);
        }

        // Remove a key
        public async Task RemoveAsync(string key)
        {
            await _cacheDb.KeyDeleteAsync(key);
        }
        #endregion
    }
}